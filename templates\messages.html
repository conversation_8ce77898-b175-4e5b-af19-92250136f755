{% extends "base.html" %}

{% block title %}Messages & Alerts - Emergency Response App{% endblock %}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="{{ url_for('landing') }}">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>

        <div class="navbar-nav ms-auto">
            <a class="nav-link" href="{{ url_for('landing') }}">
                <i class="fas fa-home me-1"></i>Home
            </a>
        </div>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-5 text-primary">
                    <i class="fas fa-comments me-3"></i>Messages & Alerts
                </h1>
                <p class="lead">Emergency notifications and communication center</p>
            </div>
        </div>
    </div>

    <!-- <PERSON><PERSON> Filters -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="btn-group" role="group">
                <button class="btn btn-outline-primary active" onclick="filterMessages('all')">All Messages</button>
                <button class="btn btn-outline-danger" onclick="filterMessages('emergency')">Emergency Alerts</button>
                <button class="btn btn-outline-warning" onclick="filterMessages('weather')">Weather Alerts</button>
                <button class="btn btn-outline-info" onclick="filterMessages('updates')">System Updates</button>
            </div>
        </div>
        <div class="col-md-4">
            <button class="btn btn-success w-100" onclick="composeMessage()">
                <i class="fas fa-plus me-2"></i>New Message
            </button>
        </div>
    </div>

    <!-- Messages List -->
    <div class="row">
        <div class="col-lg-8">
            <div id="messagesList">
                <!-- Emergency Alert -->
                <div class="card mb-3 border-danger message-item" data-type="emergency">
                    <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>EMERGENCY ALERT</strong>
                        </div>
                        <small>5 minutes ago</small>
                    </div>
                    <div class="card-body">
                        <h6>Large Fire Reported in Downtown Area</h6>
                        <p class="mb-2">Multiple fire units responding to a large structure fire at 456 Oak Street. Residents in the area should evacuate immediately. Avoid the downtown area if possible.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-danger">HIGH PRIORITY</span>
                            <div>
                                <button class="btn btn-sm btn-outline-danger me-2">View Details</button>
                                <button class="btn btn-sm btn-danger">Share Alert</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Weather Alert -->
                <div class="card mb-3 border-warning message-item" data-type="weather">
                    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-cloud-rain me-2"></i>
                            <strong>WEATHER ALERT</strong>
                        </div>
                        <small>1 hour ago</small>
                    </div>
                    <div class="card-body">
                        <h6>Severe Weather Warning</h6>
                        <p class="mb-2">High winds and dry conditions expected. Increased fire risk in the region. Outdoor burning is prohibited until further notice.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-warning text-dark">MEDIUM PRIORITY</span>
                            <div>
                                <button class="btn btn-sm btn-outline-warning me-2">View Forecast</button>
                                <button class="btn btn-sm btn-warning">Acknowledge</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Update -->
                <div class="card mb-3 border-info message-item" data-type="updates">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>SYSTEM UPDATE</strong>
                        </div>
                        <small>3 hours ago</small>
                    </div>
                    <div class="card-body">
                        <h6>New First Aid Guides Available</h6>
                        <p class="mb-2">We've added new video tutorials for emergency response procedures. Check out the updated first aid section for the latest guidance.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-info">INFORMATION</span>
                            <div>
                                <button class="btn btn-sm btn-outline-info me-2">View Updates</button>
                                <button class="btn btn-sm btn-info">Mark as Read</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Community Message -->
                <div class="card mb-3 border-success message-item" data-type="community">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-users me-2"></i>
                            <strong>COMMUNITY MESSAGE</strong>
                        </div>
                        <small>6 hours ago</small>
                    </div>
                    <div class="card-body">
                        <h6>Fire Safety Training Session</h6>
                        <p class="mb-2">Join us for a free fire safety training session this Saturday at the Community Center. Learn essential fire prevention and response techniques.</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-success">COMMUNITY</span>
                            <div>
                                <button class="btn btn-sm btn-outline-success me-2">Register</button>
                                <button class="btn btn-sm btn-success">Interested</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Load More -->
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" onclick="loadMoreMessages()">
                    <i class="fas fa-chevron-down me-2"></i>Load More Messages
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-danger" onclick="sendEmergencyAlert()">
                            <i class="fas fa-exclamation-triangle me-2"></i>Send Emergency Alert
                        </button>
                        <button class="btn btn-warning" onclick="broadcastMessage()">
                            <i class="fas fa-broadcast-tower me-2"></i>Broadcast Message
                        </button>
                        <button class="btn btn-info" onclick="checkWeatherAlerts()">
                            <i class="fas fa-cloud me-2"></i>Check Weather
                        </button>
                    </div>
                </div>
            </div>

            <!-- Message Statistics -->
            <div class="card mb-3">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Message Stats</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-danger">12</h4>
                            <small>Emergency Alerts</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-warning">8</h4>
                            <small>Weather Alerts</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">25</h4>
                            <small>System Updates</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">15</h4>
                            <small>Community</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emergency Contacts -->
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0"><i class="fas fa-phone me-2"></i>Emergency Contacts</h6>
                </div>
                <div class="card-body">
                    <div class="contact-item mb-2">
                        <strong>Fire Rescue</strong><br>
                        <a href="tel:118" class="text-danger">118</a>
                    </div>
                    <div class="contact-item mb-2">
                        <strong>Police</strong><br>
                        <a href="tel:117" class="text-primary">117</a>
                    </div>
                    <div class="contact-item mb-2">
                        <strong>Ambulance</strong><br>
                        <a href="tel:119" class="text-success">119</a>
                    </div>
                    <div class="contact-item">
                        <strong>Emergency Services</strong><br>
                        <a href="tel:118" class="text-warning">Fire: 118 | Police: 117 | Ambulance: 119</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Compose Message Modal -->
<div class="modal fade" id="composeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Compose Message</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="composeForm">
                    <div class="mb-3">
                        <label for="messageType" class="form-label">Message Type</label>
                        <select class="form-select" id="messageType">
                            <option value="emergency">Emergency Alert</option>
                            <option value="weather">Weather Alert</option>
                            <option value="updates">System Update</option>
                            <option value="community">Community Message</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="messageTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="messageTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="messageContent" class="form-label">Message</label>
                        <textarea class="form-control" id="messageContent" rows="4" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority">
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="sendMessage()">Send Message</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function filterMessages(type) {
    const messages = document.querySelectorAll('.message-item');
    const buttons = document.querySelectorAll('.btn-group .btn');

    // Update active button
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Filter messages
    messages.forEach(message => {
        if (type === 'all' || message.dataset.type === type) {
            message.style.display = 'block';
        } else {
            message.style.display = 'none';
        }
    });
}

function composeMessage() {
    const modal = new bootstrap.Modal(document.getElementById('composeModal'));
    modal.show();
}

function sendMessage() {
    const form = document.getElementById('composeForm');
    const formData = new FormData(form);

    // Simulate sending message
    alert('Message sent successfully!');

    // Close modal and reset form
    const modal = bootstrap.Modal.getInstance(document.getElementById('composeModal'));
    modal.hide();
    form.reset();
}

function sendEmergencyAlert() {
    if (confirm('Are you sure you want to send an emergency alert? This will notify all users in the area.')) {
        alert('Emergency alert sent to all users in the area!');
    }
}

function broadcastMessage() {
    composeMessage();
}

function checkWeatherAlerts() {
    alert('Checking latest weather alerts...\n\nCurrent conditions: Clear skies, low wind\nNo active weather warnings');
}

function loadMoreMessages() {
    alert('Loading more messages...');
    // In real implementation, this would load more messages from the server
}

// Auto-refresh messages every 30 seconds
setInterval(() => {
    console.log('Checking for new messages...');
    // In real implementation, this would check for new messages
}, 30000);
</script>
{% endblock %}
