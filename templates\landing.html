{% extends "base.html" %}

{% block title %}Dashboard - Emergency Response App{% endblock %}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="{{ url_for('landing') }}">
            <i class="fas fa-shield-alt me-2"></i>Emergency Response
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>{{ current_user.full_name }}
                    </span>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v me-1"></i>Menu
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                            <i class="fas fa-user me-2"></i>Profile
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('map_page') }}">
                            <i class="fas fa-map-marked-alt me-2"></i>Map
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('messages') }}">
                            <i class="fas fa-comments me-2"></i>Messages
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('help_page') }}">
                            <i class="fas fa-question-circle me-2"></i>Help
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('settings') }}">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Emergency Alert Banner -->
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Emergency Hotlines:</strong> Fire Rescue: 118 | Police: 117 | Ambulance: 119
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Main Dashboard -->
    <div class="row">
        <!-- Fire Emergency Reporting -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100 border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-fire me-2"></i>Fire Emergency Reporting
                    </h4>
                </div>
                <div class="card-body">
                    <p class="card-text">Report fire emergencies in your area quickly and efficiently.</p>

                    <!-- Location Status -->
                    <div id="locationStatus" class="alert alert-info" style="display: none;">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <span id="locationText">Getting your location...</span>
                    </div>

                    <form id="emergencyForm">
                        <div class="mb-3">
                            <label for="location" class="form-label">Location Description</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="location" placeholder="Describe the emergency location" required>
                                <button type="button" class="btn btn-outline-primary" id="detectLocationBtn">
                                    <i class="fas fa-crosshairs me-1"></i>Detect
                                </button>
                            </div>
                            <small class="form-text text-muted">Your exact GPS coordinates will be automatically included</small>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Emergency Description</label>
                            <textarea class="form-control" id="description" rows="3" placeholder="Describe what you see (fire size, smoke, people affected, etc.)" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="severity" class="form-label">Severity Level</label>
                            <select class="form-select" id="severity" required>
                                <option value="">Select severity...</option>
                                <option value="low">🟢 Low - Small fire, no immediate danger</option>
                                <option value="medium">🟡 Medium - Moderate fire, some risk</option>
                                <option value="high">🟠 High - Large fire, significant danger</option>
                                <option value="critical">🔴 Critical - Life-threatening emergency</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-danger btn-lg w-100" id="submitEmergencyBtn">
                            <i class="fas fa-exclamation-triangle me-2"></i>Report Emergency
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- First Aid Section -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100 border-success">
                <div class="card-header bg-success text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-first-aid me-2"></i>First Aid Guidance
                    </h4>
                </div>
                <div class="card-body">
                    <p class="card-text">Access comprehensive first aid guides and emergency procedures.</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('first_aid') }}" class="btn btn-success btn-lg">
                            <i class="fas fa-book-medical me-2"></i>View First Aid Guides
                        </a>
                        <button class="btn btn-outline-success" onclick="quickFirstAid()">
                            <i class="fas fa-search me-2"></i>Quick Search
                        </button>
                    </div>

                    <!-- Quick Access First Aid -->
                    <div class="mt-3">
                        <h6>Quick Access:</h6>
                        <div class="row">
                            <div class="col-6">
                                <a href="{{ url_for('first_aid_detail', practice_id=1) }}" class="btn btn-sm btn-outline-danger w-100 mb-2">CPR</a>
                            </div>
                            <div class="col-6">
                                <a href="{{ url_for('first_aid_detail', practice_id=2) }}" class="btn btn-sm btn-outline-warning w-100 mb-2">Choking</a>
                            </div>
                            <div class="col-6">
                                <a href="{{ url_for('first_aid_detail', practice_id=3) }}" class="btn btn-sm btn-outline-info w-100 mb-2">Burns</a>
                            </div>
                            <div class="col-6">
                                <a href="{{ url_for('first_aid_detail', practice_id=4) }}" class="btn btn-sm btn-outline-secondary w-100 mb-2">Bleeding</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mt-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-primary text-white">
                <div class="card-body">
                    <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                    <h5>24/7</h5>
                    <p class="mb-0">Emergency Coverage</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h5>10+</h5>
                    <p class="mb-0">First Aid Guides</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-warning text-white">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h5>&lt;2 min</h5>
                    <p class="mb-0">Response Time</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <i class="fas fa-shield-alt fa-2x mb-2"></i>
                    <h5>100%</h5>
                    <p class="mb-0">Secure & Private</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Simple global variable for location
let userLocation = null;

// Wait for page to load completely
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded - setting up location detection');

    // Get the detect button and add click event
    const detectBtn = document.getElementById('detectLocationBtn');
    if (detectBtn) {
        detectBtn.addEventListener('click', function() {
            console.log('Detect button clicked!');
            getMyLocation();
        });
        console.log('Location detect button ready');
    } else {
        console.error('Detect button not found!');
    }

    // Try to get location automatically when page loads
    setTimeout(function() {
        console.log('Attempting automatic location detection...');
        getMyLocation(true); // true = silent mode for auto-detection
    }, 1000);
});

// Simple function to get user's location
function getMyLocation(silent = false) {
    console.log('Getting location... silent mode:', silent);

    // Get page elements
    const locationInput = document.getElementById('location');
    const locationStatus = document.getElementById('locationStatus');
    const locationText = document.getElementById('locationText');
    const detectBtn = document.getElementById('detectLocationBtn');

    if (!locationInput) {
        console.error('Location input not found!');
        if (!silent) alert('Error: Location input not found!');
        return;
    }

    // Check if geolocation is supported
    if (!navigator.geolocation) {
        console.error('Geolocation not supported');
        if (!silent) alert('Geolocation is not supported by this browser');
        return;
    }

    // Update UI if not silent
    if (!silent && detectBtn) {
        detectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Getting...';
        detectBtn.disabled = true;
    }

    if (locationStatus && locationText) {
        locationStatus.style.display = 'block';
        locationStatus.className = 'alert alert-info';
        locationText.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Getting your location...';
    }

    // Get current position
    navigator.geolocation.getCurrentPosition(
        function(position) {
            console.log('SUCCESS! Got location:', position);

            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            const accuracy = Math.round(position.coords.accuracy);

            // Store location data
            userLocation = {
                latitude: lat,
                longitude: lng,
                accuracy: accuracy,
                timestamp: new Date().toISOString()
            };

            // Put coordinates in the input field RIGHT NOW
            const coordsText = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            locationInput.value = coordsText;
            console.log('Location input filled with:', coordsText);

            // Update status
            if (locationStatus && locationText) {
                locationStatus.className = 'alert alert-success';
                locationText.innerHTML = `<i class="fas fa-check-circle me-2"></i>Location detected! (±${accuracy}m accuracy)`;
            }

            // Reset button
            if (!silent && detectBtn) {
                detectBtn.innerHTML = '<i class="fas fa-check me-1"></i>Got it!';
                setTimeout(function() {
                    detectBtn.innerHTML = '<i class="fas fa-crosshairs me-1"></i>Detect';
                    detectBtn.disabled = false;
                }, 2000);
            }

            // Try to get a readable address (optional)
            getAddress(lat, lng)
                .then(function(address) {
                    if (address) {
                        console.log('Got readable address:', address);
                        locationInput.value = address;
                        if (locationText) {
                            locationText.innerHTML = `<i class="fas fa-check-circle me-2"></i>Location: ${address}`;
                        }
                    }
                })
                .catch(function(error) {
                    console.log('Address lookup failed, keeping coordinates');
                });
        },
        function(error) {
            console.error('Geolocation error:', error);

            let message = 'Could not get your location';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    message = 'Location access denied. Please allow location access.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    message = 'Location unavailable. Check your GPS settings.';
                    break;
                case error.TIMEOUT:
                    message = 'Location request timed out. Try again.';
                    break;
            }

            console.error('Error message:', message);

            if (!silent) {
                alert(message);
            }

            if (locationStatus && locationText) {
                locationStatus.className = 'alert alert-danger';
                locationText.innerHTML = `<i class="fas fa-times-circle me-2"></i>${message}`;
            }

            // Reset button
            if (!silent && detectBtn) {
                detectBtn.innerHTML = '<i class="fas fa-crosshairs me-1"></i>Detect';
                detectBtn.disabled = false;
            }
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        }
    );
}

// Simple function to get readable address
function getAddress(lat, lng) {
    return new Promise(function(resolve, reject) {
        console.log('Looking up address for:', lat, lng);

        fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`)
            .then(function(response) {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error('Address lookup failed');
                }
            })
            .then(function(data) {
                console.log('Address data:', data);

                if (data && data.locality) {
                    resolve(`${data.locality}, ${data.principalSubdivision || data.countryName}`);
                } else if (data && data.city) {
                    resolve(`${data.city}, ${data.principalSubdivision || data.countryName}`);
                } else {
                    reject(new Error('No address found'));
                }
            })
            .catch(function(error) {
                console.log('Address lookup error:', error);
                reject(error);
            });
    });
}

// Handle emergency form submission
document.getElementById('emergencyForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const submitBtn = document.getElementById('submitEmergencyBtn');
    const originalText = submitBtn.innerHTML;

    // Validate form
    const location = document.getElementById('location').value.trim();
    const description = document.getElementById('description').value.trim();
    const severity = document.getElementById('severity').value;

    if (!location || !description || !severity) {
        alert('Please fill in all required fields.');
        return;
    }

    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Reporting Emergency...';
    submitBtn.disabled = true;

    const formData = {
        location: location,
        description: description,
        severity: severity,
        latitude: userLocation ? userLocation.latitude : null,
        longitude: userLocation ? userLocation.longitude : null,
        location_accuracy: userLocation ? userLocation.accuracy : null,
        timestamp: new Date().toISOString()
    };

    fetch('/report-emergency', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                <strong>Emergency Reported Successfully!</strong><br>
                Report ID: ${data.report_id}<br>
                Emergency services have been notified and will respond shortly.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Insert alert at the top of the card body
            const cardBody = document.querySelector('.card-body');
            cardBody.insertBefore(alertDiv, cardBody.firstChild);

            // Reset form
            this.reset();

            // Reset location status
            if (userLocation) {
                const locationStatus = document.getElementById('locationStatus');
                const locationText = document.getElementById('locationText');
                locationStatus.className = 'alert alert-success';
                locationText.innerHTML = `<i class="fas fa-check-circle me-2"></i>Location detected (±${Math.round(userLocation.accuracy)}m accuracy)`;
            }

            // Scroll to top to show success message
            window.scrollTo({ top: 0, behavior: 'smooth' });

        } else {
            throw new Error(data.message || 'Unknown error occurred');
        }
    })
    .catch(error => {
        console.error('Emergency reporting error:', error);
        alert('Error reporting emergency. Please try again or call emergency services directly at 118.');
    })
    .finally(() => {
        // Restore button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function quickFirstAid() {
    const search = prompt('What type of emergency are you dealing with?');
    if (search) {
        window.location.href = "{{ url_for('first_aid') }}?search=" + encodeURIComponent(search);
    }
}
</script>
{% endblock %}
