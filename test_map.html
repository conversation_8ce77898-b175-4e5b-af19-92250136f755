<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Map</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #map {
            height: 500px;
            width: 100%;
            border: 2px solid #ccc;
            border-radius: 8px;
        }
        .controls {
            margin-bottom: 10px;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>OpenStreetMap Test</h1>
    
    <div class="controls">
        <button class="btn" onclick="testMap()">Initialize Map</button>
        <button class="btn" onclick="alert('JavaScript is working!')">Test JS</button>
        <button class="btn" onclick="console.log('Console test')">Test Console</button>
    </div>
    
    <div id="map">
        <div style="padding: 20px; text-align: center; color: #666;">
            Click "Initialize Map" to load the map
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <script>
        console.log('Script loaded');
        
        function testMap() {
            console.log('Testing map initialization...');
            const mapElement = document.getElementById('map');
            console.log('Map element found:', mapElement);
            
            if (!mapElement) {
                console.error('Map element not found!');
                return;
            }
            
            // Clear any existing content
            mapElement.innerHTML = '';
            
            // Test if Leaflet is loaded
            if (typeof L === 'undefined') {
                console.error('Leaflet not loaded!');
                mapElement.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Error: Leaflet library not loaded</div>';
                return;
            }
            
            console.log('Leaflet loaded successfully');
            
            try {
                // Create a simple map
                const map = L.map('map').setView([3.8480, 11.5021], 6);
                
                // Add OpenStreetMap tiles
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);
                
                // Add a simple marker
                L.marker([3.8480, 11.5021])
                    .addTo(map)
                    .bindPopup('Yaoundé, Cameroon')
                    .openPopup();
                    
                console.log('Map created successfully!');
                
            } catch (error) {
                console.error('Error creating map:', error);
                mapElement.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Error creating map: ' + error.message + '</div>';
            }
        }
        
        // Auto-initialize after page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            setTimeout(function() {
                console.log('Auto-initializing map...');
                testMap();
            }, 2000);
        });
    </script>
</body>
</html>
