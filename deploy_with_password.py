#!/usr/bin/env python3
"""
Emergency Response App - VPS Deployment with Password Authentication
Deploys the application to VPS: ************* (srv838312.hstgr.cloud)
"""

import paramiko
import os
import sys
import time
from pathlib import Path

# VPS Configuration
VPS_HOST = "*************"
VPS_USER = "root"
VPS_PASSWORD = "Software_2025"
VPS_HOSTNAME = "srv838312.hstgr.cloud"

class VPSDeployer:
    def __init__(self):
        self.ssh = None
        self.sftp = None
        
    def connect(self):
        """Connect to VPS via SSH with password"""
        try:
            print(f"🔌 Connecting to VPS: {VPS_HOST}")
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connect with password
            self.ssh.connect(
                hostname=VPS_HOST,
                username=VPS_USER,
                password=VPS_PASSWORD,
                timeout=30
            )
            
            self.sftp = self.ssh.open_sftp()
            print("✅ Connected successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def execute_command(self, command, description="", timeout=300):
        """Execute command on VPS"""
        if description:
            print(f"\n🔧 {description}")
        print(f"   Command: {command}")
        
        try:
            stdin, stdout, stderr = self.ssh.exec_command(command, timeout=timeout)
            exit_code = stdout.channel.recv_exit_status()
            
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if exit_code == 0:
                print(f"   ✅ Success")
                if output.strip():
                    print(f"   Output: {output.strip()[:200]}...")
            else:
                print(f"   ❌ Failed (exit code: {exit_code})")
                if error.strip():
                    print(f"   Error: {error.strip()}")
                    
            return exit_code == 0, output, error
            
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False, "", str(e)
    
    def upload_file(self, local_path, remote_path, description=""):
        """Upload file to VPS"""
        if description:
            print(f"\n📤 {description}")
        print(f"   {local_path} → {remote_path}")
        
        try:
            if os.path.exists(local_path):
                # Create remote directory if needed
                remote_dir = os.path.dirname(remote_path)
                self.execute_command(f"mkdir -p {remote_dir}")
                
                self.sftp.put(local_path, remote_path)
                print(f"   ✅ Uploaded successfully")
                return True
            else:
                print(f"   ⚠️  Warning: {local_path} not found locally")
                return False
                
        except Exception as e:
            print(f"   ❌ Upload failed: {e}")
            return False
    
    def upload_directory(self, local_dir, remote_dir, description=""):
        """Upload entire directory to VPS"""
        if description:
            print(f"\n📁 {description}")
        
        if not os.path.exists(local_dir):
            print(f"   ⚠️  Warning: {local_dir} not found locally")
            return False
        
        try:
            # Create remote directory
            self.execute_command(f"mkdir -p {remote_dir}")
            
            # Upload all files in directory
            for root, dirs, files in os.walk(local_dir):
                for file in files:
                    local_file = os.path.join(root, file)
                    relative_path = os.path.relpath(local_file, local_dir)
                    remote_file = f"{remote_dir}/{relative_path}".replace("\\", "/")
                    
                    # Create remote subdirectory if needed
                    remote_subdir = os.path.dirname(remote_file)
                    self.execute_command(f"mkdir -p {remote_subdir}")
                    
                    self.sftp.put(local_file, remote_file)
                    print(f"   📄 {relative_path}")
            
            print(f"   ✅ Directory uploaded successfully")
            return True
            
        except Exception as e:
            print(f"   ❌ Directory upload failed: {e}")
            return False
    
    def deploy(self):
        """Main deployment function"""
        print("🚀 EMERGENCY RESPONSE APP - VPS DEPLOYMENT")
        print("="*60)
        print(f"Target VPS: {VPS_HOST} ({VPS_HOSTNAME})")
        print(f"User: {VPS_USER}")
        print("="*60)
        
        if not self.connect():
            return False
        
        try:
            # Step 1: Update system and install packages
            print("\n📍 Step 1: Installing system packages...")
            self.execute_command("apt update", "Updating package cache")
            self.execute_command("DEBIAN_FRONTEND=noninteractive apt install -y python3 python3-pip python3-venv git nginx sqlite3 curl wget unzip htop vim ufw", "Installing system packages")
            
            # Step 2: Create application user and directories
            print("\n📍 Step 2: Setting up application user...")
            self.execute_command("useradd -r -s /bin/bash -d /opt/emergency-app emergency || true", "Creating emergency user")
            self.execute_command("mkdir -p /opt/emergency-app /var/log/emergency-app", "Creating directories")
            self.execute_command("chown emergency:emergency /opt/emergency-app /var/log/emergency-app", "Setting ownership")
            
            # Step 3: Upload application files
            print("\n📍 Step 3: Uploading application files...")
            files_to_upload = [
                ("app.py", "/opt/emergency-app/app.py"),
                ("database.py", "/opt/emergency-app/database.py"),
                ("auth.py", "/opt/emergency-app/auth.py"),
                ("api_endpoints.py", "/opt/emergency-app/api_endpoints.py"),
                ("docker-compose.monitoring.yml", "/opt/emergency-app/docker-compose.monitoring.yml"),
            ]
            
            for local_file, remote_file in files_to_upload:
                self.upload_file(local_file, remote_file, f"Uploading {local_file}")
            
            # Upload directories
            self.upload_directory("templates", "/opt/emergency-app/templates", "Uploading templates directory")
            self.upload_directory("static", "/opt/emergency-app/static", "Uploading static directory")
            self.upload_directory("monitoring", "/opt/emergency-app/monitoring", "Uploading monitoring directory")
            
            # Step 4: Setup Python environment
            print("\n📍 Step 4: Setting up Python environment...")
            self.execute_command("cd /opt/emergency-app && python3 -m venv venv", "Creating virtual environment")
            self.execute_command("cd /opt/emergency-app && source venv/bin/activate && pip install flask flask-login prometheus-client prometheus-flask-exporter bcrypt email-validator", "Installing Python packages", timeout=600)
            self.execute_command("chown -R emergency:emergency /opt/emergency-app", "Setting file permissions")
            
            # Step 5: Create systemd service
            print("\n📍 Step 5: Creating systemd service...")
            service_content = """[Unit]
Description=Emergency Response App
After=network.target

[Service]
Type=simple
User=emergency
Group=emergency
WorkingDirectory=/opt/emergency-app
Environment=PATH=/opt/emergency-app/venv/bin
ExecStart=/opt/emergency-app/venv/bin/python app.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=emergency-app

[Install]
WantedBy=multi-user.target"""
            
            self.execute_command(f"cat > /etc/systemd/system/emergency-app.service << 'EOF'\n{service_content}\nEOF", "Creating systemd service")
            self.execute_command("systemctl daemon-reload", "Reloading systemd")
            self.execute_command("systemctl enable emergency-app", "Enabling emergency-app service")
            
            # Step 6: Configure nginx
            print("\n📍 Step 6: Configuring nginx...")
            nginx_config = f"""server {{
    listen 80;
    server_name {VPS_HOSTNAME} {VPS_HOST};

    location / {{
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }}

    location /metrics {{
        proxy_pass http://127.0.0.1:3000/metrics;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}

    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    access_log /var/log/nginx/emergency-app-access.log;
    error_log /var/log/nginx/emergency-app-error.log;
}}"""
            
            self.execute_command(f"cat > /etc/nginx/sites-available/emergency-app << 'EOF'\n{nginx_config}\nEOF", "Creating nginx configuration")
            self.execute_command("ln -sf /etc/nginx/sites-available/emergency-app /etc/nginx/sites-enabled/", "Enabling nginx site")
            self.execute_command("rm -f /etc/nginx/sites-enabled/default", "Removing default site")
            self.execute_command("nginx -t", "Testing nginx configuration")
            self.execute_command("systemctl enable nginx", "Enabling nginx")
            
            # Step 7: Configure firewall
            print("\n📍 Step 7: Configuring firewall...")
            self.execute_command("ufw --force reset", "Resetting firewall")
            self.execute_command("ufw default deny incoming", "Setting default deny")
            self.execute_command("ufw default allow outgoing", "Setting default allow outgoing")
            
            ports = [22, 80, 443, 3000, 9090, 3001, 9093, 9100]
            for port in ports:
                self.execute_command(f"ufw allow {port}", f"Opening port {port}")
            
            self.execute_command("ufw --force enable", "Enabling firewall")
            
            # Step 8: Start services
            print("\n📍 Step 8: Starting services...")
            self.execute_command("systemctl start emergency-app", "Starting emergency app")
            self.execute_command("systemctl reload nginx", "Reloading nginx")
            
            # Step 9: Install Docker
            print("\n📍 Step 9: Installing Docker...")
            self.execute_command("curl -fsSL https://get.docker.com -o get-docker.sh", "Downloading Docker installer")
            self.execute_command("sh get-docker.sh", "Installing Docker", timeout=600)
            self.execute_command("curl -L 'https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-linux-x86_64' -o /usr/local/bin/docker-compose", "Installing Docker Compose")
            self.execute_command("chmod +x /usr/local/bin/docker-compose", "Making Docker Compose executable")
            self.execute_command("systemctl enable docker", "Enabling Docker")
            self.execute_command("systemctl start docker", "Starting Docker")
            
            # Step 10: Start monitoring stack
            print("\n📍 Step 10: Starting monitoring stack...")
            self.execute_command("cd /opt/emergency-app && docker-compose -f docker-compose.monitoring.yml up -d", "Starting monitoring services", timeout=600)
            
            # Step 11: Verify deployment
            print("\n📍 Step 11: Verifying deployment...")
            self.execute_command("systemctl is-active emergency-app nginx docker", "Checking service status")
            self.execute_command("curl -s http://localhost:3000/api/v1/health", "Testing application health")
            self.execute_command("docker ps", "Checking Docker containers")
            
            print("\n" + "="*60)
            print("🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!")
            print("="*60)
            print(f"🌐 Application URL: http://{VPS_HOST}")
            print(f"🌐 Application URL: http://{VPS_HOSTNAME}")
            print(f"🔗 API Health: http://{VPS_HOST}/api/v1/health")
            print(f"📊 Metrics: http://{VPS_HOST}/metrics")
            print(f"📈 Prometheus: http://{VPS_HOST}:9090")
            print(f"📊 Grafana: http://{VPS_HOST}:3001 (admin/emergency123)")
            print(f"🚨 Alertmanager: http://{VPS_HOST}:9093")
            print("="*60)
            
            return True
            
        except Exception as e:
            print(f"❌ Deployment failed: {e}")
            return False
        
        finally:
            if self.ssh:
                self.ssh.close()
            if self.sftp:
                self.sftp.close()

def main():
    """Main function"""
    print("🔧 Emergency Response App VPS Deployment Tool")
    print("This will deploy the application to your VPS server.")
    print(f"Target: {VPS_HOST} ({VPS_HOSTNAME})")
    print()
    
    # Check if we're in the right directory
    if not os.path.exists("app.py"):
        print("❌ Error: app.py not found in current directory")
        print("Please run this script from the Emergency Response App directory")
        return False
    
    # Start deployment
    deployer = VPSDeployer()
    success = deployer.deploy()
    
    if success:
        print("\n🎊 Deployment completed successfully!")
        print("Your Emergency Response App is now running on the VPS!")
    else:
        print("\n💥 Deployment failed!")
        print("Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    main()
