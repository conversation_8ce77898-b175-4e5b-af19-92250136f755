#!/usr/bin/env python3
"""
Fix Grafana login credentials
"""

import paramiko

VPS_HOST = "*************"
VPS_USER = "root"
VPS_PASSWORD = "Software_2025"

def fix_grafana_login():
    try:
        print("🔧 Fixing Grafana login credentials...")
        
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname=VPS_HOST, username=VPS_USER, password=VPS_PASSWORD, timeout=30)
        
        commands = [
            ("cd /opt/emergency-app && docker-compose -f docker-compose.monitoring.yml stop grafana", "Stopping Grafana container"),
            ("docker rm emergency-grafana || true", "Removing Grafana container"),
            ("docker volume rm loope_grafana_data || true", "Removing Grafana data volume"),
            ("cd /opt/emergency-app && docker-compose -f docker-compose.monitoring.yml up -d grafana", "Starting fresh Grafana container"),
            ("sleep 15", "Waiting for <PERSON><PERSON> to start"),
            ("curl -s http://localhost:3001/api/health", "Testing Grafana health"),
            ("docker logs emergency-grafana --tail 20", "Checking Grafana logs"),
        ]
        
        for cmd, desc in commands:
            print(f"\n🔧 {desc}")
            print(f"   Command: {cmd}")
            
            stdin, stdout, stderr = ssh.exec_command(cmd, timeout=60)
            exit_code = stdout.channel.recv_exit_status()
            
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if exit_code == 0:
                print(f"   ✅ Success")
                if output.strip():
                    print(f"   Output: {output.strip()[:300]}...")
            else:
                print(f"   ⚠️  Exit code: {exit_code}")
                if error.strip():
                    print(f"   Error: {error.strip()[:200]}...")
        
        # Test login with default credentials
        print("\n🔧 Testing Grafana login")
        test_login_cmd = "curl -X POST -H 'Content-Type: application/json' -d '{\"user\":\"admin\",\"password\":\"emergency123\"}' http://localhost:3001/login"
        stdin, stdout, stderr = ssh.exec_command(test_login_cmd)
        output = stdout.read().decode('utf-8')
        print(f"   Login test result: {output}")
        
        # Also try default Grafana credentials
        print("\n🔧 Testing with default Grafana credentials")
        test_default_cmd = "curl -X POST -H 'Content-Type: application/json' -d '{\"user\":\"admin\",\"password\":\"admin\"}' http://localhost:3001/login"
        stdin, stdout, stderr = ssh.exec_command(test_default_cmd)
        output = stdout.read().decode('utf-8')
        print(f"   Default login test result: {output}")
        
        print("\n" + "="*60)
        print("🎯 GRAFANA LOGIN INFORMATION")
        print("="*60)
        print(f"URL: http://{VPS_HOST}:3001")
        print("Try these credentials:")
        print("1. Username: admin, Password: emergency123")
        print("2. Username: admin, Password: admin (default)")
        print("3. If neither works, Grafana may still be starting up")
        print("="*60)
        
        ssh.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    fix_grafana_login()
