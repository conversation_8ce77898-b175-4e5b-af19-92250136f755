#!/usr/bin/env python3
"""
Get <PERSON> initial admin password and verify installation
"""

import paramiko
import time

VPS_HOST = "***********"
VPS_USER = "root"
VPS_PASSWORD = "Software-2025"

def get_jenkins_info():
    try:
        print("🔍 Getting Jenkins information...")
        
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname=VPS_HOST, username=VPS_USER, password=VPS_PASSWORD, timeout=30)
        
        print("✅ Connected to VPS!")
        
        # Check Jenkins container status
        print("\n🐳 Checking Jenkins container status...")
        stdin, stdout, stderr = ssh.exec_command("docker ps | grep jenkins")
        output = stdout.read().decode('utf-8')
        print(f"   Containers: {output}")
        
        # Wait a bit more for <PERSON> to fully initialize
        print("\n⏳ Waiting for <PERSON> to fully initialize...")
        time.sleep(30)
        
        # Try to get the initial admin password
        print("\n🔑 Attempting to get <PERSON> initial admin password...")
        
        # Method 1: Direct file access
        stdin, stdout, stderr = ssh.exec_command("docker exec jenkins-emergency-app cat /var/jenkins_home/secrets/initialAdminPassword")
        password_output = stdout.read().decode('utf-8').strip()
        password_error = stderr.read().decode('utf-8').strip()
        
        if password_output and not password_error:
            print(f"   ✅ Initial Admin Password: {password_output}")
        else:
            print(f"   ⚠️  Could not get password directly: {password_error}")
            
            # Method 2: Check Jenkins logs for password
            print("   🔍 Checking Jenkins logs for password...")
            stdin, stdout, stderr = ssh.exec_command("docker logs jenkins-emergency-app 2>&1 | grep -A 5 -B 5 'password'")
            log_output = stdout.read().decode('utf-8')
            if log_output:
                print(f"   📝 Log output:\n{log_output}")
            
            # Method 3: Check if Jenkins is using setup wizard
            stdin, stdout, stderr = ssh.exec_command("docker logs jenkins-emergency-app 2>&1 | grep 'Jenkins initial setup'")
            setup_output = stdout.read().decode('utf-8')
            if setup_output:
                print(f"   🔧 Setup info: {setup_output}")
        
        # Test Jenkins accessibility
        print("\n🌐 Testing Jenkins accessibility...")
        stdin, stdout, stderr = ssh.exec_command("curl -s -I http://localhost:8080")
        curl_output = stdout.read().decode('utf-8')
        if "200 OK" in curl_output:
            print("   ✅ Jenkins is accessible on port 8080")
        else:
            print(f"   ⚠️  Jenkins response: {curl_output}")
        
        # Check Jenkins version
        print("\n📋 Getting Jenkins version...")
        stdin, stdout, stderr = ssh.exec_command("docker exec jenkins-emergency-app java -jar /usr/share/jenkins/jenkins.war --version")
        version_output = stdout.read().decode('utf-8').strip()
        if version_output:
            print(f"   📦 Jenkins Version: {version_output}")
        
        # Check available plugins
        print("\n🔌 Checking Jenkins plugins directory...")
        stdin, stdout, stderr = ssh.exec_command("docker exec jenkins-emergency-app ls -la /var/jenkins_home/plugins/ | head -10")
        plugins_output = stdout.read().decode('utf-8')
        if plugins_output:
            print(f"   📁 Plugins directory: {plugins_output}")
        
        # Get Jenkins container logs (last 20 lines)
        print("\n📝 Recent Jenkins logs...")
        stdin, stdout, stderr = ssh.exec_command("docker logs jenkins-emergency-app --tail 20")
        recent_logs = stdout.read().decode('utf-8')
        if recent_logs:
            print(f"   📄 Recent logs:\n{recent_logs}")
        
        ssh.close()
        
        print("\n" + "="*60)
        print("🎯 JENKINS ACCESS INFORMATION")
        print("="*60)
        print(f"🌐 Jenkins URL: http://{VPS_HOST}:8080")
        print(f"🌐 Jenkins URL: http://srv878357.hstgr.cloud:8080")
        print("👤 Username: admin")
        if password_output and not password_error:
            print(f"🔑 Password: {password_output}")
        else:
            print("🔑 Password: Check container logs or use setup wizard")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    get_jenkins_info()
