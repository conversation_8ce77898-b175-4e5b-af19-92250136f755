# Emergency Response App - VPS Deployment Script (PowerShell)
# Target VPS: ************* (srv838312.hstgr.cloud)

$VPS_HOST = "*************"
$VPS_HOSTNAME = "srv838312.hstgr.cloud"
$VPS_USER = "root"

Write-Host "========================================" -ForegroundColor Green
Write-Host "Emergency Response App - VPS Deployment" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "Target VPS: $VPS_HOST" -ForegroundColor Yellow
Write-Host "Hostname: $VPS_HOSTNAME" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

function Execute-SSH {
    param(
        [string]$Command,
        [string]$Description
    )
    
    Write-Host "`n🔧 $Description" -ForegroundColor Cyan
    Write-Host "   Command: $Command" -ForegroundColor Gray
    
    try {
        $result = ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no "$VPS_USER@$VPS_HOST" $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ Success" -ForegroundColor Green
            if ($result) {
                Write-Host "   Output: $($result -join ' ' | Out-String -Stream | Select-Object -First 3)" -ForegroundColor Gray
            }
            return $true
        } else {
            Write-Host "   ❌ Failed (exit code: $LASTEXITCODE)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "   ❌ Exception: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Upload-File {
    param(
        [string]$LocalPath,
        [string]$RemotePath,
        [string]$Description
    )
    
    Write-Host "`n📤 $Description" -ForegroundColor Cyan
    Write-Host "   $LocalPath → $RemotePath" -ForegroundColor Gray
    
    try {
        if (Test-Path $LocalPath) {
            scp -o ConnectTimeout=30 -o StrictHostKeyChecking=no $LocalPath "$VPS_USER@$VPS_HOST`:$RemotePath"
            if ($LASTEXITCODE -eq 0) {
                Write-Host "   ✅ Uploaded successfully" -ForegroundColor Green
                return $true
            } else {
                Write-Host "   ❌ Upload failed (exit code: $LASTEXITCODE)" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "   ⚠️  Warning: $LocalPath not found locally" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "   ❌ Upload exception: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Step 1: Test SSH connectivity
Write-Host "`n📍 Step 1: Testing SSH connectivity..." -ForegroundColor Magenta
if (-not (Execute-SSH "echo 'SSH connection successful!'" "Testing SSH connection")) {
    Write-Host "`n❌ ERROR: Cannot connect to VPS via SSH" -ForegroundColor Red
    Write-Host "Please ensure:" -ForegroundColor Yellow
    Write-Host "1. SSH key is properly configured" -ForegroundColor Yellow
    Write-Host "2. VPS is accessible" -ForegroundColor Yellow
    Write-Host "3. SSH service is running" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 2: Update system packages
Write-Host "`n📍 Step 2: Installing system packages..." -ForegroundColor Magenta
Execute-SSH "apt update" "Updating package cache"
Execute-SSH "apt install -y python3 python3-pip python3-venv git nginx sqlite3 curl wget unzip htop vim ufw" "Installing system packages"

# Step 3: Create application user and directories
Write-Host "`n📍 Step 3: Setting up application user..." -ForegroundColor Magenta
Execute-SSH "useradd -r -s /bin/bash -d /opt/emergency-app emergency || true" "Creating emergency user"
Execute-SSH "mkdir -p /opt/emergency-app /var/log/emergency-app" "Creating directories"
Execute-SSH "chown emergency:emergency /opt/emergency-app /var/log/emergency-app" "Setting ownership"

# Step 4: Upload application files
Write-Host "`n📍 Step 4: Uploading application files..." -ForegroundColor Magenta
$files = @("app.py", "database.py", "auth.py", "api_endpoints.py")
foreach ($file in $files) {
    Upload-File $file "/opt/emergency-app/$file" "Uploading $file"
}

# Step 5: Create directories and upload templates/static
Write-Host "`n📍 Step 5: Setting up templates and static files..." -ForegroundColor Magenta
Execute-SSH "mkdir -p /opt/emergency-app/templates /opt/emergency-app/static" "Creating template directories"

if (Test-Path "templates") {
    Write-Host "📤 Uploading templates directory..." -ForegroundColor Cyan
    scp -r -o ConnectTimeout=30 -o StrictHostKeyChecking=no templates/* "$VPS_USER@$VPS_HOST`:/opt/emergency-app/templates/"
}

if (Test-Path "static") {
    Write-Host "📤 Uploading static directory..." -ForegroundColor Cyan
    scp -r -o ConnectTimeout=30 -o StrictHostKeyChecking=no static/* "$VPS_USER@$VPS_HOST`:/opt/emergency-app/static/"
}

# Step 6: Setup Python environment
Write-Host "`n📍 Step 6: Setting up Python environment..." -ForegroundColor Magenta
Execute-SSH "cd /opt/emergency-app && python3 -m venv venv" "Creating virtual environment"
Execute-SSH "cd /opt/emergency-app && source venv/bin/activate && pip install flask flask-login prometheus-client prometheus-flask-exporter bcrypt email-validator" "Installing Python packages"

# Step 7: Create systemd service
Write-Host "`n📍 Step 7: Creating systemd service..." -ForegroundColor Magenta
$serviceContent = @"
[Unit]
Description=Emergency Response App
After=network.target

[Service]
Type=simple
User=emergency
Group=emergency
WorkingDirectory=/opt/emergency-app
Environment=PATH=/opt/emergency-app/venv/bin
ExecStart=/opt/emergency-app/venv/bin/python app.py
ExecReload=/bin/kill -HUP `$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=emergency-app

[Install]
WantedBy=multi-user.target
"@

Execute-SSH "cat > /etc/systemd/system/emergency-app.service << 'EOF'`n$serviceContent`nEOF" "Creating systemd service file"

# Step 8: Configure nginx
Write-Host "`n📍 Step 8: Configuring nginx..." -ForegroundColor Magenta
$nginxConfig = @"
server {
    listen 80;
    server_name $VPS_HOSTNAME $VPS_HOST;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /metrics {
        proxy_pass http://127.0.0.1:3000/metrics;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
    }

    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    access_log /var/log/nginx/emergency-app-access.log;
    error_log /var/log/nginx/emergency-app-error.log;
}
"@

Execute-SSH "cat > /etc/nginx/sites-available/emergency-app << 'EOF'`n$nginxConfig`nEOF" "Creating nginx configuration"

# Step 9: Enable services
Write-Host "`n📍 Step 9: Enabling services..." -ForegroundColor Magenta
Execute-SSH "ln -sf /etc/nginx/sites-available/emergency-app /etc/nginx/sites-enabled/" "Enabling nginx site"
Execute-SSH "rm -f /etc/nginx/sites-enabled/default" "Removing default site"
Execute-SSH "nginx -t" "Testing nginx configuration"
Execute-SSH "systemctl enable nginx emergency-app" "Enabling services"
Execute-SSH "systemctl daemon-reload" "Reloading systemd"

# Step 10: Set permissions
Write-Host "`n📍 Step 10: Setting file permissions..." -ForegroundColor Magenta
Execute-SSH "chown -R emergency:emergency /opt/emergency-app" "Setting file ownership"

# Step 11: Configure firewall
Write-Host "`n📍 Step 11: Configuring firewall..." -ForegroundColor Magenta
Execute-SSH "ufw --force reset" "Resetting firewall"
Execute-SSH "ufw default deny incoming" "Setting default deny"
Execute-SSH "ufw default allow outgoing" "Setting default allow outgoing"

$ports = @(22, 80, 443, 3000, 9090, 3001, 9093, 9100)
foreach ($port in $ports) {
    Execute-SSH "ufw allow $port" "Opening port $port"
}

Execute-SSH "ufw --force enable" "Enabling firewall"

# Step 12: Start services
Write-Host "`n📍 Step 12: Starting services..." -ForegroundColor Magenta
Execute-SSH "systemctl start emergency-app" "Starting emergency app"
Execute-SSH "systemctl reload nginx" "Reloading nginx"

# Step 13: Install Docker
Write-Host "`n📍 Step 13: Installing Docker..." -ForegroundColor Magenta
Execute-SSH "curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh" "Installing Docker"
Execute-SSH "curl -L https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-linux-x86_64 -o /usr/local/bin/docker-compose" "Installing Docker Compose"
Execute-SSH "chmod +x /usr/local/bin/docker-compose" "Making Docker Compose executable"

# Step 14: Upload monitoring configuration
Write-Host "`n📍 Step 14: Uploading monitoring configuration..." -ForegroundColor Magenta
Upload-File "docker-compose.monitoring.yml" "/opt/emergency-app/docker-compose.monitoring.yml" "Uploading Docker Compose config"

if (Test-Path "monitoring") {
    Write-Host "📤 Uploading monitoring directory..." -ForegroundColor Cyan
    scp -r -o ConnectTimeout=30 -o StrictHostKeyChecking=no monitoring "$VPS_USER@$VPS_HOST`:/opt/emergency-app/"
}

# Step 15: Verify deployment
Write-Host "`n📍 Step 15: Verifying deployment..." -ForegroundColor Magenta
Execute-SSH "systemctl is-active emergency-app nginx" "Checking service status"
Execute-SSH "curl -s http://localhost:3000/api/v1/health" "Testing application health"

# Final summary
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "🎉 DEPLOYMENT COMPLETED!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Your Emergency Response App is now running on:" -ForegroundColor Yellow
Write-Host "- http://$VPS_HOST" -ForegroundColor Cyan
Write-Host "- http://$VPS_HOSTNAME" -ForegroundColor Cyan
Write-Host ""
Write-Host "API Endpoints:" -ForegroundColor Yellow
Write-Host "- Health: http://$VPS_HOST/api/v1/health" -ForegroundColor Cyan
Write-Host "- Metrics: http://$VPS_HOST/metrics" -ForegroundColor Cyan
Write-Host ""
Write-Host "To start monitoring stack:" -ForegroundColor Yellow
Write-Host "ssh root@$VPS_HOST `"cd /opt/emergency-app && docker-compose -f docker-compose.monitoring.yml up -d`"" -ForegroundColor Cyan
Write-Host ""
Write-Host "Monitoring URLs (after starting monitoring stack):" -ForegroundColor Yellow
Write-Host "- Prometheus: http://$VPS_HOST`:9090" -ForegroundColor Cyan
Write-Host "- Grafana: http://$VPS_HOST`:3001 (admin/emergency123)" -ForegroundColor Cyan
Write-Host "- Alertmanager: http://$VPS_HOST`:9093" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Green

Read-Host "`nPress Enter to exit"
