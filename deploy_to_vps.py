#!/usr/bin/env python3
"""
Emergency Response App - Direct VPS Deployment Script
Deploys the application directly to VPS via SSH
"""

import paramiko
import os
import sys
import time
from pathlib import Path

# VPS Configuration
VPS_HOST = "*************"
VPS_USER = "root"
VPS_HOSTNAME = "srv838312.hstgr.cloud"
APP_PORT = 3000
MONITORING_PORTS = [9090, 3001, 9093, 9100]

class VPSDeployer:
    def __init__(self):
        self.ssh = None
        self.sftp = None
        
    def connect(self):
        """Connect to VPS via SSH"""
        try:
            print(f"🔌 Connecting to VPS: {VPS_HOST}")
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Try to connect
            self.ssh.connect(
                hostname=VPS_HOST,
                username=VPS_USER,
                timeout=30,
                banner_timeout=30
            )
            
            self.sftp = self.ssh.open_sftp()
            print("✅ Connected successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            print("\n🔧 Troubleshooting:")
            print("1. Ensure SSH key is properly configured")
            print("2. Check if VPS is accessible")
            print("3. Verify SSH service is running on VPS")
            return False
    
    def execute_command(self, command, description=""):
        """Execute command on VPS"""
        if description:
            print(f"\n🔧 {description}")
        print(f"   Command: {command}")
        
        try:
            stdin, stdout, stderr = self.ssh.exec_command(command, timeout=300)
            exit_code = stdout.channel.recv_exit_status()
            
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if exit_code == 0:
                print(f"   ✅ Success")
                if output.strip():
                    print(f"   Output: {output.strip()[:200]}...")
            else:
                print(f"   ❌ Failed (exit code: {exit_code})")
                if error.strip():
                    print(f"   Error: {error.strip()}")
                    
            return exit_code == 0, output, error
            
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False, "", str(e)
    
    def upload_file(self, local_path, remote_path, description=""):
        """Upload file to VPS"""
        if description:
            print(f"\n📤 {description}")
        print(f"   {local_path} → {remote_path}")
        
        try:
            # Create remote directory if needed
            remote_dir = os.path.dirname(remote_path)
            self.execute_command(f"mkdir -p {remote_dir}")
            
            self.sftp.put(local_path, remote_path)
            print(f"   ✅ Uploaded successfully")
            return True
            
        except Exception as e:
            print(f"   ❌ Upload failed: {e}")
            return False
    
    def deploy_system_packages(self):
        """Install system packages and dependencies"""
        print("\n" + "="*60)
        print("📦 INSTALLING SYSTEM PACKAGES")
        print("="*60)
        
        commands = [
            ("apt update", "Updating package cache"),
            ("apt install -y python3 python3-pip python3-venv git nginx sqlite3 curl wget unzip htop vim ufw", "Installing system packages"),
            ("systemctl enable nginx", "Enabling nginx"),
            ("systemctl start nginx", "Starting nginx"),
        ]
        
        for cmd, desc in commands:
            success, _, _ = self.execute_command(cmd, desc)
            if not success:
                print(f"❌ Failed to execute: {desc}")
                return False
        
        return True
    
    def setup_application_user(self):
        """Create application user and directories"""
        print("\n" + "="*60)
        print("👤 SETTING UP APPLICATION USER")
        print("="*60)
        
        commands = [
            ("useradd -r -s /bin/bash -d /opt/emergency-app emergency || true", "Creating emergency user"),
            ("mkdir -p /opt/emergency-app", "Creating app directory"),
            ("chown emergency:emergency /opt/emergency-app", "Setting directory ownership"),
            ("mkdir -p /var/log/emergency-app", "Creating log directory"),
            ("chown emergency:emergency /var/log/emergency-app", "Setting log directory ownership"),
        ]
        
        for cmd, desc in commands:
            self.execute_command(cmd, desc)
        
        return True
    
    def deploy_application_files(self):
        """Upload and deploy application files"""
        print("\n" + "="*60)
        print("📁 DEPLOYING APPLICATION FILES")
        print("="*60)
        
        # Files to upload
        files_to_upload = [
            ("app.py", "/opt/emergency-app/app.py"),
            ("database.py", "/opt/emergency-app/database.py"),
            ("auth.py", "/opt/emergency-app/auth.py"),
            ("api_endpoints.py", "/opt/emergency-app/api_endpoints.py"),
        ]
        
        # Upload main application files
        for local_file, remote_file in files_to_upload:
            if os.path.exists(local_file):
                self.upload_file(local_file, remote_file, f"Uploading {local_file}")
            else:
                print(f"⚠️  Warning: {local_file} not found locally")
        
        # Upload templates and static directories
        self.execute_command("mkdir -p /opt/emergency-app/templates /opt/emergency-app/static")
        
        # Set permissions
        self.execute_command("chown -R emergency:emergency /opt/emergency-app", "Setting file permissions")
        
        return True
    
    def setup_python_environment(self):
        """Setup Python virtual environment and install dependencies"""
        print("\n" + "="*60)
        print("🐍 SETTING UP PYTHON ENVIRONMENT")
        print("="*60)
        
        commands = [
            ("cd /opt/emergency-app && python3 -m venv venv", "Creating virtual environment"),
            ("cd /opt/emergency-app && source venv/bin/activate && pip install flask flask-login prometheus-client prometheus-flask-exporter bcrypt email-validator", "Installing Python packages"),
        ]
        
        for cmd, desc in commands:
            success, _, _ = self.execute_command(cmd, desc)
            if not success:
                print(f"❌ Failed: {desc}")
                return False
        
        return True
    
    def setup_systemd_service(self):
        """Create and start systemd service"""
        print("\n" + "="*60)
        print("⚙️  SETTING UP SYSTEMD SERVICE")
        print("="*60)
        
        # Create systemd service file
        service_content = """[Unit]
Description=Emergency Response App
After=network.target

[Service]
Type=simple
User=emergency
Group=emergency
WorkingDirectory=/opt/emergency-app
Environment=PATH=/opt/emergency-app/venv/bin
ExecStart=/opt/emergency-app/venv/bin/python app.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=emergency-app

[Install]
WantedBy=multi-user.target
"""
        
        # Write service file
        with open("emergency-app.service", "w") as f:
            f.write(service_content)
        
        self.upload_file("emergency-app.service", "/etc/systemd/system/emergency-app.service", "Uploading systemd service")
        
        commands = [
            ("systemctl daemon-reload", "Reloading systemd"),
            ("systemctl enable emergency-app", "Enabling emergency-app service"),
            ("systemctl start emergency-app", "Starting emergency-app service"),
        ]
        
        for cmd, desc in commands:
            self.execute_command(cmd, desc)
        
        # Clean up local service file
        if os.path.exists("emergency-app.service"):
            os.remove("emergency-app.service")
        
        return True
    
    def setup_nginx(self):
        """Configure nginx reverse proxy"""
        print("\n" + "="*60)
        print("🌐 CONFIGURING NGINX")
        print("="*60)
        
        nginx_config = f"""server {{
    listen 80;
    server_name {VPS_HOSTNAME} {VPS_HOST};

    location / {{
        proxy_pass http://127.0.0.1:{APP_PORT};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }}

    location /metrics {{
        proxy_pass http://127.0.0.1:{APP_PORT}/metrics;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    # Logging
    access_log /var/log/nginx/emergency-app-access.log;
    error_log /var/log/nginx/emergency-app-error.log;
}}
"""
        
        # Write nginx config
        with open("emergency-app-nginx.conf", "w") as f:
            f.write(nginx_config)
        
        self.upload_file("emergency-app-nginx.conf", "/etc/nginx/sites-available/emergency-app", "Uploading nginx config")
        
        commands = [
            ("ln -sf /etc/nginx/sites-available/emergency-app /etc/nginx/sites-enabled/", "Enabling nginx site"),
            ("rm -f /etc/nginx/sites-enabled/default", "Removing default site"),
            ("nginx -t", "Testing nginx configuration"),
            ("systemctl reload nginx", "Reloading nginx"),
        ]
        
        for cmd, desc in commands:
            self.execute_command(cmd, desc)
        
        # Clean up local config file
        if os.path.exists("emergency-app-nginx.conf"):
            os.remove("emergency-app-nginx.conf")
        
        return True
    
    def setup_firewall(self):
        """Configure UFW firewall"""
        print("\n" + "="*60)
        print("🔥 CONFIGURING FIREWALL")
        print("="*60)
        
        ports_to_open = [22, 80, 443, APP_PORT] + MONITORING_PORTS
        
        commands = [
            ("ufw --force reset", "Resetting firewall"),
            ("ufw default deny incoming", "Setting default deny"),
            ("ufw default allow outgoing", "Setting default allow outgoing"),
        ]
        
        for port in ports_to_open:
            commands.append((f"ufw allow {port}", f"Opening port {port}"))
        
        commands.append(("ufw --force enable", "Enabling firewall"))
        
        for cmd, desc in commands:
            self.execute_command(cmd, desc)
        
        return True
    
    def install_docker(self):
        """Install Docker and Docker Compose"""
        print("\n" + "="*60)
        print("🐳 INSTALLING DOCKER")
        print("="*60)
        
        commands = [
            ("apt install -y apt-transport-https ca-certificates curl gnupg lsb-release", "Installing Docker dependencies"),
            ("curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg", "Adding Docker GPG key"),
            ('echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null', "Adding Docker repository"),
            ("apt update", "Updating package cache"),
            ("apt install -y docker-ce docker-ce-cli containerd.io", "Installing Docker"),
            ("systemctl enable docker", "Enabling Docker"),
            ("systemctl start docker", "Starting Docker"),
            ("curl -L https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-linux-x86_64 -o /usr/local/bin/docker-compose", "Installing Docker Compose"),
            ("chmod +x /usr/local/bin/docker-compose", "Making Docker Compose executable"),
        ]
        
        for cmd, desc in commands:
            success, _, _ = self.execute_command(cmd, desc)
            if not success and "docker" in cmd:
                print(f"⚠️  Warning: {desc} failed, but continuing...")
        
        return True
    
    def verify_deployment(self):
        """Verify that the deployment was successful"""
        print("\n" + "="*60)
        print("✅ VERIFYING DEPLOYMENT")
        print("="*60)
        
        checks = [
            ("systemctl is-active emergency-app", "Emergency app service"),
            ("systemctl is-active nginx", "Nginx service"),
            ("systemctl is-active docker", "Docker service"),
            (f"curl -s -o /dev/null -w '%{{http_code}}' http://localhost:{APP_PORT}/api/v1/health", "Application health check"),
            ("ps aux | grep python | grep app.py", "Application process"),
        ]
        
        all_good = True
        for cmd, desc in checks:
            success, output, _ = self.execute_command(cmd, desc)
            if not success:
                all_good = False
        
        return all_good
    
    def deploy(self):
        """Main deployment function"""
        print("🚀 EMERGENCY RESPONSE APP - VPS DEPLOYMENT")
        print("="*60)
        print(f"Target VPS: {VPS_HOST} ({VPS_HOSTNAME})")
        print(f"User: {VPS_USER}")
        print("="*60)
        
        if not self.connect():
            return False
        
        try:
            # Deployment steps
            steps = [
                self.deploy_system_packages,
                self.setup_application_user,
                self.deploy_application_files,
                self.setup_python_environment,
                self.setup_systemd_service,
                self.setup_nginx,
                self.setup_firewall,
                self.install_docker,
                self.verify_deployment,
            ]
            
            for i, step in enumerate(steps, 1):
                print(f"\n📍 Step {i}/{len(steps)}: {step.__name__}")
                if not step():
                    print(f"❌ Step {i} failed: {step.__name__}")
                    return False
            
            print("\n" + "="*60)
            print("🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!")
            print("="*60)
            print(f"🌐 Application URL: http://{VPS_HOST}")
            print(f"🌐 Application URL: http://{VPS_HOSTNAME}")
            print(f"🔗 API Health: http://{VPS_HOST}/api/v1/health")
            print(f"📊 Metrics: http://{VPS_HOST}/metrics")
            print("="*60)
            
            return True
            
        except Exception as e:
            print(f"❌ Deployment failed: {e}")
            return False
        
        finally:
            if self.ssh:
                self.ssh.close()
            if self.sftp:
                self.sftp.close()

def main():
    """Main function"""
    deployer = VPSDeployer()
    
    print("🔧 Emergency Response App VPS Deployment Tool")
    print("This will deploy the application to your VPS server.")
    print(f"Target: {VPS_HOST} ({VPS_HOSTNAME})")
    print()
    
    # Check if we're in the right directory
    if not os.path.exists("app.py"):
        print("❌ Error: app.py not found in current directory")
        print("Please run this script from the Emergency Response App directory")
        return False
    
    # Start deployment
    success = deployer.deploy()
    
    if success:
        print("\n🎊 Deployment completed successfully!")
        print("Your Emergency Response App is now running on the VPS!")
    else:
        print("\n💥 Deployment failed!")
        print("Please check the error messages above and try again.")
    
    return success

if __name__ == "__main__":
    main()
