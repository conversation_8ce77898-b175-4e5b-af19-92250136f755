# Emergency Response App - Quick Deployment Commands
# Copy and paste these commands one by one into your VPS terminal

# ============================================================================
# STEP 1: Connect to VPS
# ============================================================================
ssh root@*************

# ============================================================================
# STEP 2: System Setup (run on VPS)
# ============================================================================
apt update && apt install -y python3 python3-pip python3-venv git nginx sqlite3 curl wget unzip htop vim ufw

# ============================================================================
# STEP 3: Create User and Directories (run on VPS)
# ============================================================================
useradd -r -s /bin/bash -d /opt/emergency-app emergency || true
mkdir -p /opt/emergency-app /var/log/emergency-app
chown emergency:emergency /opt/emergency-app /var/log/emergency-app

# ============================================================================
# STEP 4: Upload Files (run from your LOCAL machine)
# ============================================================================
# Open a NEW terminal/command prompt on your local machine and run:
cd c:\Users\<USER>\Desktop\loopes\loope
scp app.py root@*************:/opt/emergency-app/
scp database.py root@*************:/opt/emergency-app/
scp auth.py root@*************:/opt/emergency-app/
scp api_endpoints.py root@*************:/opt/emergency-app/
scp -r templates root@*************:/opt/emergency-app/
scp -r static root@*************:/opt/emergency-app/
scp docker-compose.monitoring.yml root@*************:/opt/emergency-app/
scp -r monitoring root@*************:/opt/emergency-app/

# ============================================================================
# STEP 5: Setup Python Environment (run on VPS)
# ============================================================================
cd /opt/emergency-app
python3 -m venv venv
source venv/bin/activate
pip install flask flask-login prometheus-client prometheus-flask-exporter bcrypt email-validator
chown -R emergency:emergency /opt/emergency-app

# ============================================================================
# STEP 6: Create Systemd Service (run on VPS)
# ============================================================================
cat > /etc/systemd/system/emergency-app.service << 'EOF'
[Unit]
Description=Emergency Response App
After=network.target

[Service]
Type=simple
User=emergency
Group=emergency
WorkingDirectory=/opt/emergency-app
Environment=PATH=/opt/emergency-app/venv/bin
ExecStart=/opt/emergency-app/venv/bin/python app.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=emergency-app

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable emergency-app

# ============================================================================
# STEP 7: Configure Nginx (run on VPS)
# ============================================================================
cat > /etc/nginx/sites-available/emergency-app << 'EOF'
server {
    listen 80;
    server_name srv838312.hstgr.cloud *************;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /metrics {
        proxy_pass http://127.0.0.1:3000/metrics;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    access_log /var/log/nginx/emergency-app-access.log;
    error_log /var/log/nginx/emergency-app-error.log;
}
EOF

ln -sf /etc/nginx/sites-available/emergency-app /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t
systemctl enable nginx
systemctl reload nginx

# ============================================================================
# STEP 8: Configure Firewall (run on VPS)
# ============================================================================
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow 22
ufw allow 80
ufw allow 443
ufw allow 3000
ufw allow 9090
ufw allow 3001
ufw allow 9093
ufw allow 9100
ufw --force enable

# ============================================================================
# STEP 9: Start Application (run on VPS)
# ============================================================================
systemctl start emergency-app
systemctl status emergency-app

# Test the application
curl http://localhost:3000/api/v1/health

# ============================================================================
# STEP 10: Install Docker (run on VPS)
# ============================================================================
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-linux-x86_64" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
systemctl enable docker
systemctl start docker

# ============================================================================
# STEP 11: Start Monitoring Stack (run on VPS)
# ============================================================================
cd /opt/emergency-app
docker-compose -f docker-compose.monitoring.yml up -d
docker ps

# ============================================================================
# VERIFICATION COMMANDS (run on VPS)
# ============================================================================
# Check all services
systemctl status emergency-app nginx docker

# Test application endpoints
curl http://localhost:3000/api/v1/health
curl http://localhost:3000/metrics

# Test external access
curl http://*************/api/v1/health

# Check monitoring services
curl http://localhost:9090/-/healthy
curl http://localhost:3001/api/health

# ============================================================================
# SUCCESS! Your app should now be accessible at:
# ============================================================================
# Main App: http://*************
# Main App: http://srv838312.hstgr.cloud
# API Health: http://*************/api/v1/health
# Prometheus: http://*************:9090
# Grafana: http://*************:3001 (admin/emergency123)
# Alertmanager: http://*************:9093
# ============================================================================
