[tool:pytest]
# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test execution options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=term-missing
    --junitxml=test-results.xml
    --cov-fail-under=80

# Coverage configuration
[coverage:run]
source = .
omit = 
    venv/*
    tests/*
    setup.py
    */migrations/*
    */venv/*
    */virtualenvs/*
    */site-packages/*
    */__pycache__/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    security: Security tests
    performance: Performance tests
    smoke: Smoke tests
    api: API tests
    database: Database tests
    auth: Authentication tests
    staging: Tests that run against staging environment
    production: Tests that run against production environment

# Minimum version requirements
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# addopts = -n auto  # Uncomment to enable parallel execution with pytest-xdist

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filter warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
