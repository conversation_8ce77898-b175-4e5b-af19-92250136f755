@echo off
echo ========================================
echo Emergency Response App - VPS Deployment
echo ========================================
echo Target VPS: *************
echo Hostname: srv838312.hstgr.cloud
echo ========================================

echo.
echo Step 1: Testing SSH connectivity...
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@************* "echo 'SSH connection successful!'"
if %errorlevel% neq 0 (
    echo ERROR: Cannot connect to VPS via SSH
    echo Please ensure:
    echo 1. SSH key is properly configured
    echo 2. VPS is accessible
    echo 3. SSH service is running
    pause
    exit /b 1
)

echo.
echo Step 2: Updating system packages...
ssh root@************* "apt update && apt install -y python3 python3-pip python3-venv git nginx sqlite3 curl wget unzip htop vim ufw"

echo.
echo Step 3: Creating application user and directories...
ssh root@************* "useradd -r -s /bin/bash -d /opt/emergency-app emergency || true"
ssh root@************* "mkdir -p /opt/emergency-app /var/log/emergency-app"
ssh root@************* "chown emergency:emergency /opt/emergency-app /var/log/emergency-app"

echo.
echo Step 4: Uploading application files...
scp app.py root@*************:/opt/emergency-app/
scp database.py root@*************:/opt/emergency-app/
scp auth.py root@*************:/opt/emergency-app/
scp api_endpoints.py root@*************:/opt/emergency-app/

echo.
echo Step 5: Creating templates and static directories...
ssh root@************* "mkdir -p /opt/emergency-app/templates /opt/emergency-app/static"
scp -r templates/* root@*************:/opt/emergency-app/templates/ 2>nul
scp -r static/* root@*************:/opt/emergency-app/static/ 2>nul

echo.
echo Step 6: Setting up Python environment...
ssh root@************* "cd /opt/emergency-app && python3 -m venv venv"
ssh root@************* "cd /opt/emergency-app && source venv/bin/activate && pip install flask flask-login prometheus-client prometheus-flask-exporter bcrypt email-validator"

echo.
echo Step 7: Creating systemd service...
ssh root@************* "cat > /etc/systemd/system/emergency-app.service << 'EOF'
[Unit]
Description=Emergency Response App
After=network.target

[Service]
Type=simple
User=emergency
Group=emergency
WorkingDirectory=/opt/emergency-app
Environment=PATH=/opt/emergency-app/venv/bin
ExecStart=/opt/emergency-app/venv/bin/python app.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=emergency-app

[Install]
WantedBy=multi-user.target
EOF"

echo.
echo Step 8: Configuring nginx...
ssh root@************* "cat > /etc/nginx/sites-available/emergency-app << 'EOF'
server {
    listen 80;
    server_name srv838312.hstgr.cloud *************;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /metrics {
        proxy_pass http://127.0.0.1:3000/metrics;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    add_header X-Frame-Options \"SAMEORIGIN\" always;
    add_header X-XSS-Protection \"1; mode=block\" always;
    add_header X-Content-Type-Options \"nosniff\" always;

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    access_log /var/log/nginx/emergency-app-access.log;
    error_log /var/log/nginx/emergency-app-error.log;
}
EOF"

echo.
echo Step 9: Enabling services...
ssh root@************* "ln -sf /etc/nginx/sites-available/emergency-app /etc/nginx/sites-enabled/"
ssh root@************* "rm -f /etc/nginx/sites-enabled/default"
ssh root@************* "nginx -t"
ssh root@************* "systemctl enable nginx emergency-app"
ssh root@************* "systemctl daemon-reload"

echo.
echo Step 10: Setting file permissions...
ssh root@************* "chown -R emergency:emergency /opt/emergency-app"

echo.
echo Step 11: Configuring firewall...
ssh root@************* "ufw --force reset"
ssh root@************* "ufw default deny incoming"
ssh root@************* "ufw default allow outgoing"
ssh root@************* "ufw allow 22"
ssh root@************* "ufw allow 80"
ssh root@************* "ufw allow 443"
ssh root@************* "ufw allow 3000"
ssh root@************* "ufw allow 9090"
ssh root@************* "ufw allow 3001"
ssh root@************* "ufw allow 9093"
ssh root@************* "ufw --force enable"

echo.
echo Step 12: Starting services...
ssh root@************* "systemctl start emergency-app"
ssh root@************* "systemctl reload nginx"

echo.
echo Step 13: Installing Docker for monitoring...
ssh root@************* "curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh"
ssh root@************* "curl -L https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-linux-x86_64 -o /usr/local/bin/docker-compose"
ssh root@************* "chmod +x /usr/local/bin/docker-compose"

echo.
echo Step 14: Uploading monitoring configuration...
scp docker-compose.monitoring.yml root@*************:/opt/emergency-app/ 2>nul
scp -r monitoring root@*************:/opt/emergency-app/ 2>nul

echo.
echo Step 15: Verifying deployment...
echo Checking service status...
ssh root@************* "systemctl is-active emergency-app nginx"

echo.
echo Testing application health...
ssh root@************* "curl -s http://localhost:3000/api/v1/health"

echo.
echo ========================================
echo DEPLOYMENT COMPLETED!
echo ========================================
echo.
echo Your Emergency Response App is now running on:
echo - http://*************
echo - http://srv838312.hstgr.cloud
echo.
echo API Endpoints:
echo - Health: http://*************/api/v1/health
echo - Metrics: http://*************/metrics
echo.
echo To start monitoring stack:
echo ssh root@************* "cd /opt/emergency-app && docker-compose -f docker-compose.monitoring.yml up -d"
echo.
echo Monitoring URLs (after starting monitoring stack):
echo - Prometheus: http://*************:9090
echo - Grafana: http://*************:3001 (admin/emergency123)
echo - Alertmanager: http://*************:9093
echo ========================================

pause
