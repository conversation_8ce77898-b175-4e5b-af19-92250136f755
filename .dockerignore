# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
*.md
docs/

# CI/CD
.github/
Jenkinsfile
CI_CD_SETUP.md
SETUP_GUIDE.md

# Testing
tests/
pytest.ini
.pytest_cache/
htmlcov/
.coverage
test-results.xml
*-report.json

# Build artifacts
build/
dist/
*.egg-info/
*.tar.gz

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp

# Backup files
backup-*
*.bak

# Development files
docker-compose.yml
docker-compose.override.yml
.env
.env.local
.env.development
.env.test
.env.production

# Monitoring (included in separate monitoring compose)
monitoring/

# Ansible
ansible/
