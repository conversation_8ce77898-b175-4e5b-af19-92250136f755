#!/usr/bin/env python3
"""
Test SSH connection to VPS
"""

import paramiko
import sys

VPS_HOST = "*************"
VPS_USER = "root"
VPS_PASSWORD = "Software_2025"

def test_connection():
    try:
        print(f"Testing SSH connection to {VPS_HOST}...")
        
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        # Connect with password
        ssh.connect(
            hostname=VPS_HOST,
            username=VPS_USER,
            password=VPS_PASSWORD,
            timeout=30
        )
        
        print("✅ SSH connection successful!")
        
        # Test a simple command
        stdin, stdout, stderr = ssh.exec_command("whoami")
        result = stdout.read().decode('utf-8').strip()
        print(f"✅ Command test successful: {result}")
        
        # Test another command
        stdin, stdout, stderr = ssh.exec_command("uname -a")
        result = stdout.read().decode('utf-8').strip()
        print(f"✅ System info: {result}")
        
        ssh.close()
        return True
        
    except Exception as e:
        print(f"❌ SSH connection failed: {e}")
        return False

if __name__ == "__main__":
    if test_connection():
        print("\n🎉 SSH connection is working! Ready for deployment.")
    else:
        print("\n💥 SSH connection failed. Please check credentials.")
        sys.exit(1)
