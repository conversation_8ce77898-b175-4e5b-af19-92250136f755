#!/usr/bin/env python3
"""
Fix Docker and start monitoring stack
"""

import paramiko

VPS_HOST = "*************"
VPS_USER = "root"
VPS_PASSWORD = "Software_2025"

def fix_docker():
    try:
        print("🔧 Fixing Docker and starting monitoring stack...")
        
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname=VPS_HOST, username=VPS_USER, password=VPS_PASSWORD, timeout=30)
        
        commands = [
            ("systemctl stop docker", "Stopping Docker"),
            ("systemctl start docker", "Starting Docker"),
            ("systemctl status docker --no-pager", "Checking Docker status"),
            ("docker --version", "Checking Docker version"),
            ("cd /opt/emergency-app && docker-compose -f docker-compose.monitoring.yml up -d", "Starting monitoring stack"),
            ("docker ps", "Checking running containers"),
            ("curl -s http://localhost:3000/api/v1/health", "Testing app health"),
        ]
        
        for cmd, desc in commands:
            print(f"\n🔧 {desc}")
            print(f"   Command: {cmd}")
            
            stdin, stdout, stderr = ssh.exec_command(cmd, timeout=120)
            exit_code = stdout.channel.recv_exit_status()
            
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if exit_code == 0:
                print(f"   ✅ Success")
                if output.strip():
                    print(f"   Output: {output.strip()[:300]}...")
            else:
                print(f"   ❌ Failed (exit code: {exit_code})")
                if error.strip():
                    print(f"   Error: {error.strip()}")
        
        ssh.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    fix_docker()
