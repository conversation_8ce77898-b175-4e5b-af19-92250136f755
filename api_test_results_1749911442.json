[{"name": "Health Check", "method": "GET", "endpoint": "/health", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:41.814981"}, {"name": "System Status", "method": "GET", "endpoint": "/status", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:41.823500"}, {"name": "User Login", "method": "POST", "endpoint": "/auth/login", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.053430"}, {"name": "User Registration", "method": "POST", "endpoint": "/auth/register", "status_code": 400, "expected_status": 201, "result": "FAIL", "timestamp": "2025-06-14T15:30:42.278115"}, {"name": "Get All Emergencies", "method": "GET", "endpoint": "/emergencies", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.278115"}, {"name": "Get Filtered Emergencies", "method": "GET", "endpoint": "/emergencies?status=pending&limit=5", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.294369"}, {"name": "Create Emergency Report", "method": "POST", "endpoint": "/emergencies", "status_code": 201, "expected_status": 201, "result": "PASS", "timestamp": "2025-06-14T15:30:42.307113"}, {"name": "Get Specific Emergency", "method": "GET", "endpoint": "/emergencies/6", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.313885"}, {"name": "Update Emergency Status", "method": "PUT", "endpoint": "/emergencies/6/status", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.327676"}, {"name": "Get Fire Departments", "method": "GET", "endpoint": "/fire-departments", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.334912"}, {"name": "Get Community Messages", "method": "GET", "endpoint": "/messages", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.340894"}, {"name": "Create Community Message", "method": "POST", "endpoint": "/messages", "status_code": 201, "expected_status": 201, "result": "PASS", "timestamp": "2025-06-14T15:30:42.353744"}, {"name": "Get First Aid Practices", "method": "GET", "endpoint": "/first-aid", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.358816"}, {"name": "Get Filtered First Aid", "method": "GET", "endpoint": "/first-aid?category=Cardiac Emergency&difficulty=Intermediate", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.363440"}, {"name": "Get Specific First Aid Practice", "method": "GET", "endpoint": "/first-aid/1", "status_code": 200, "expected_status": 200, "result": "PASS", "timestamp": "2025-06-14T15:30:42.370424"}, {"name": "Invalid API Key Test", "method": "GET", "endpoint": "/emergencies", "status_code": 401, "expected_status": 401, "result": "PASS", "timestamp": "2025-06-14T15:30:42.374259"}, {"name": "Missing API Key Test", "method": "GET", "endpoint": "/emergencies", "status_code": 401, "expected_status": 401, "result": "PASS", "timestamp": "2025-06-14T15:30:42.374259"}, {"name": "Invalid Endpoint Test", "method": "GET", "endpoint": "/nonexistent", "status_code": 404, "expected_status": 404, "result": "PASS", "timestamp": "2025-06-14T15:30:42.393030"}]