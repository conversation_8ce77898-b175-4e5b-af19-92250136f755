# AlertManager configuration for Emergency Response App
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'

# The directory from which notification templates are read.
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# The root route on which each incoming alert enters.
route:
  # The labels by which incoming alerts are grouped together.
  group_by: ['alertname', 'severity']
  
  # When a new group of alerts is created by an incoming alert, wait at
  # least 'group_wait' to send the initial notification.
  group_wait: 10s
  
  # When the first notification was sent, wait 'group_interval' to send a batch
  # of new alerts that started firing for that group.
  group_interval: 10s
  
  # If an alert has successfully been sent, wait 'repeat_interval' to
  # resend them.
  repeat_interval: 1h
  
  # A default receiver
  receiver: 'emergency-team'
  
  # All the above attributes are inherited by all child routes and can
  # overwritten on each.
  routes:
    # Critical alerts go to emergency team immediately
    - match:
        severity: critical
      receiver: 'emergency-team-critical'
      group_wait: 0s
      repeat_interval: 5m
    
    # Emergency reports alerts
    - match:
        alertname: CriticalEmergencyReports
      receiver: 'emergency-responders'
      group_wait: 0s
      repeat_interval: 1m

# Inhibition rules allow to mute a set of alerts given that another alert is firing.
inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']

receivers:
  - name: 'emergency-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[EMERGENCY APP] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
    
    # Webhook for integration with other systems
    webhook_configs:
      - url: 'http://localhost:3000/webhook/alerts'
        send_resolved: true

  - name: 'emergency-team-critical'
    email_configs:
      - to: '<EMAIL>, <EMAIL>'
        subject: '[CRITICAL] Emergency App Alert: {{ .GroupLabels.alertname }}'
        body: |
          🚨 CRITICAL ALERT 🚨
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Time: {{ .StartsAt }}
          {{ end }}
          
          Please investigate immediately!
    
    # SMS/Slack notifications for critical alerts (configure as needed)
    webhook_configs:
      - url: 'http://localhost:3000/webhook/critical-alerts'
        send_resolved: true

  - name: 'emergency-responders'
    email_configs:
      - to: '<EMAIL>'
        subject: '[EMERGENCY REPORT] Critical Emergency Reported'
        body: |
          🚨 EMERGENCY REPORT RECEIVED 🚨
          
          A critical emergency has been reported through the Emergency Response App.
          
          {{ range .Alerts }}
          Time: {{ .StartsAt }}
          Description: {{ .Annotations.description }}
          {{ end }}
          
          Please respond immediately according to emergency protocols.
    
    webhook_configs:
      - url: 'http://localhost:3000/webhook/emergency-reports'
        send_resolved: false
