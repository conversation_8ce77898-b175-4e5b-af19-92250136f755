<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Location Detection Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3>🧪 Location Detection Test</h3>
                    </div>
                    <div class="card-body">
                        <!-- Status Display -->
                        <div id="locationStatus" class="alert alert-info">
                            <span id="locationText">Ready to test location detection...</span>
                        </div>
                        
                        <!-- Location Input -->
                        <div class="mb-3">
                            <label for="location" class="form-label">Location Description</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="location" placeholder="Location will appear here..." readonly>
                                <button type="button" class="btn btn-primary" id="detectLocationBtn" onclick="detectLocation()">
                                    <i class="fas fa-crosshairs me-1"></i>Detect Location
                                </button>
                            </div>
                        </div>
                        
                        <!-- Debug Info -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6>🔍 Debug Information</h6>
                            </div>
                            <div class="card-body">
                                <div id="debugInfo">
                                    <p><strong>Geolocation Support:</strong> <span id="geoSupport">Checking...</span></p>
                                    <p><strong>Permissions:</strong> <span id="permissions">Unknown</span></p>
                                    <p><strong>Last Error:</strong> <span id="lastError">None</span></p>
                                    <p><strong>Coordinates:</strong> <span id="coordinates">None</span></p>
                                    <p><strong>Address:</strong> <span id="address">None</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Test Buttons -->
                        <div class="mt-3">
                            <button class="btn btn-success me-2" onclick="testGeolocation()">Test Geolocation API</button>
                            <button class="btn btn-warning me-2" onclick="testPermissions()">Check Permissions</button>
                            <button class="btn btn-info me-2" onclick="clearDebug()">Clear Debug</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let userLocation = null;
        
        // Check geolocation support on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkGeolocationSupport();
        });
        
        function checkGeolocationSupport() {
            const geoSupport = document.getElementById('geoSupport');
            if (navigator.geolocation) {
                geoSupport.innerHTML = '<span class="text-success">✅ Supported</span>';
                logDebug('Geolocation API is supported');
            } else {
                geoSupport.innerHTML = '<span class="text-danger">❌ Not Supported</span>';
                logDebug('Geolocation API is NOT supported');
            }
        }
        
        function detectLocation() {
            const btn = document.getElementById('detectLocationBtn');
            const locationInput = document.getElementById('location');
            const locationStatus = document.getElementById('locationStatus');
            const locationText = document.getElementById('locationText');
            
            logDebug('🎯 Detect button clicked');
            
            // Update button state
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Detecting...';
            btn.disabled = true;
            
            // Update status
            locationStatus.className = 'alert alert-info';
            locationText.textContent = 'Requesting location access...';
            
            if (!navigator.geolocation) {
                logDebug('❌ Geolocation not supported');
                showError('Geolocation is not supported by this browser');
                resetButton(btn);
                return;
            }
            
            logDebug('📍 Calling getCurrentPosition...');
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    logDebug('✅ Location obtained successfully');
                    
                    userLocation = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        timestamp: new Date().toISOString()
                    };
                    
                    // Update debug info
                    document.getElementById('coordinates').textContent = 
                        `${userLocation.latitude.toFixed(6)}, ${userLocation.longitude.toFixed(6)} (±${Math.round(userLocation.accuracy)}m)`;
                    
                    logDebug(`📍 Coordinates: ${userLocation.latitude}, ${userLocation.longitude}`);
                    logDebug(`🎯 Accuracy: ±${Math.round(userLocation.accuracy)}m`);
                    
                    // Try reverse geocoding
                    reverseGeocode(userLocation.latitude, userLocation.longitude)
                        .then(address => {
                            if (address) {
                                locationInput.value = address;
                                document.getElementById('address').textContent = address;
                                locationStatus.className = 'alert alert-success';
                                locationText.innerHTML = `<i class="fas fa-check-circle me-2"></i>Location detected: ${address}`;
                                logDebug(`🏠 Address found: ${address}`);
                            } else {
                                // Fallback to coordinates
                                const coordsText = `${userLocation.latitude.toFixed(6)}, ${userLocation.longitude.toFixed(6)}`;
                                locationInput.value = coordsText;
                                document.getElementById('address').textContent = 'Reverse geocoding failed';
                                locationStatus.className = 'alert alert-warning';
                                locationText.innerHTML = `<i class="fas fa-map-marker-alt me-2"></i>GPS coordinates: ${coordsText}`;
                                logDebug('🏠 Address lookup failed, using coordinates');
                            }
                        })
                        .catch(error => {
                            logDebug(`❌ Reverse geocoding error: ${error.message}`);
                            // Fallback to coordinates
                            const coordsText = `${userLocation.latitude.toFixed(6)}, ${userLocation.longitude.toFixed(6)}`;
                            locationInput.value = coordsText;
                            document.getElementById('address').textContent = `Error: ${error.message}`;
                            locationStatus.className = 'alert alert-warning';
                            locationText.innerHTML = `<i class="fas fa-map-marker-alt me-2"></i>GPS coordinates: ${coordsText}`;
                        });
                    
                    resetButton(btn);
                },
                function(error) {
                    logDebug(`❌ Geolocation error: ${error.message} (Code: ${error.code})`);
                    
                    let errorMessage = '';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = 'Location access denied by user';
                            document.getElementById('permissions').innerHTML = '<span class="text-danger">❌ Denied</span>';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = 'Location information unavailable';
                            break;
                        case error.TIMEOUT:
                            errorMessage = 'Location request timed out';
                            break;
                        default:
                            errorMessage = 'Unknown location error';
                            break;
                    }
                    
                    document.getElementById('lastError').textContent = errorMessage;
                    showError(errorMessage);
                    resetButton(btn);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        }
        
        async function reverseGeocode(lat, lng) {
            try {
                logDebug(`🌍 Starting reverse geocoding for ${lat}, ${lng}`);
                
                // Try BigDataCloud first
                const response = await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`);
                
                if (response.ok) {
                    const data = await response.json();
                    logDebug('🌍 BigDataCloud response:', data);
                    
                    if (data && data.locality) {
                        return `${data.locality}, ${data.principalSubdivision || data.countryName}`;
                    } else if (data && data.city) {
                        return `${data.city}, ${data.principalSubdivision || data.countryName}`;
                    }
                }
                
                logDebug('🌍 BigDataCloud failed, trying Nominatim...');
                
                // Try Nominatim as fallback
                const nomResponse = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);
                
                if (nomResponse.ok) {
                    const nomData = await nomResponse.json();
                    logDebug('🌍 Nominatim response:', nomData);
                    
                    if (nomData && nomData.display_name) {
                        const parts = nomData.display_name.split(',');
                        if (parts.length >= 2) {
                            return `${parts[0].trim()}, ${parts[1].trim()}`;
                        }
                        return parts[0].trim();
                    }
                }
                
                return null;
            } catch (error) {
                logDebug(`❌ Reverse geocoding error: ${error.message}`);
                throw error;
            }
        }
        
        function testGeolocation() {
            logDebug('🧪 Testing basic geolocation...');
            
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        logDebug(`✅ Basic test successful: ${position.coords.latitude}, ${position.coords.longitude}`);
                        alert(`Location test successful!\nLat: ${position.coords.latitude}\nLng: ${position.coords.longitude}\nAccuracy: ±${Math.round(position.coords.accuracy)}m`);
                    },
                    function(error) {
                        logDebug(`❌ Basic test failed: ${error.message}`);
                        alert(`Location test failed: ${error.message}`);
                    }
                );
            } else {
                logDebug('❌ Geolocation not supported');
                alert('Geolocation is not supported');
            }
        }
        
        function testPermissions() {
            if (navigator.permissions) {
                navigator.permissions.query({name: 'geolocation'}).then(function(result) {
                    logDebug(`🔐 Permission state: ${result.state}`);
                    document.getElementById('permissions').innerHTML = `<span class="text-info">${result.state}</span>`;
                    alert(`Geolocation permission: ${result.state}`);
                });
            } else {
                logDebug('❌ Permissions API not supported');
                alert('Permissions API not supported');
            }
        }
        
        function showError(message) {
            const locationStatus = document.getElementById('locationStatus');
            const locationText = document.getElementById('locationText');
            
            locationStatus.className = 'alert alert-danger';
            locationText.innerHTML = `<i class="fas fa-times-circle me-2"></i>${message}`;
        }
        
        function resetButton(btn) {
            btn.innerHTML = '<i class="fas fa-crosshairs me-1"></i>Detect Location';
            btn.disabled = false;
        }
        
        function logDebug(message) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] ${message}`);
            
            // Also add to debug display
            const debugInfo = document.getElementById('debugInfo');
            const logEntry = document.createElement('div');
            logEntry.className = 'small text-muted';
            logEntry.textContent = `[${timestamp}] ${message}`;
            debugInfo.appendChild(logEntry);
            
            // Keep only last 10 log entries
            const logs = debugInfo.querySelectorAll('.small');
            if (logs.length > 15) {
                logs[0].remove();
            }
        }
        
        function clearDebug() {
            const debugInfo = document.getElementById('debugInfo');
            const logs = debugInfo.querySelectorAll('.small');
            logs.forEach(log => log.remove());
            
            document.getElementById('lastError').textContent = 'None';
            document.getElementById('coordinates').textContent = 'None';
            document.getElementById('address').textContent = 'None';
            document.getElementById('location').value = '';
            
            const locationStatus = document.getElementById('locationStatus');
            const locationText = document.getElementById('locationText');
            locationStatus.className = 'alert alert-info';
            locationText.textContent = 'Debug cleared. Ready to test...';
        }
    </script>
</body>
</html>
