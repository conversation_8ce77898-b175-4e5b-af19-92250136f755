{% extends "base.html" %}

{% block title %}Fire Department Dashboard - Emergency Response App{% endblock %}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-dark bg-danger">
    <div class="container">
        <a class="navbar-brand" href="{{ url_for('fire_department_dashboard') }}">
            <i class="fas fa-fire-extinguisher me-2"></i>Fire Department Dashboard
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>{{ current_user.department_name or current_user.full_name }}
                    </span>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v me-1"></i>Menu
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('map_page') }}">
                            <i class="fas fa-map-marked-alt me-2"></i>Emergency Map
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('messages') }}">
                            <i class="fas fa-comments me-2"></i>Communications
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('settings') }}">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Emergency Status Banner -->
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Active Emergencies:</strong> {{ active_reports|length }} reports pending response
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Dashboard Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-danger text-white">
                <div class="card-body">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h4>{{ active_reports|length }}</h4>
                    <p class="mb-0">Active Reports</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-warning text-white">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4>{{ responding_reports|length }}</h4>
                    <p class="mb-0">Responding</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4>{{ resolved_today }}</h4>
                    <p class="mb-0">Resolved Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <i class="fas fa-fire-extinguisher fa-2x mb-2"></i>
                    <h4>{{ current_user.department_name or 'Fire Dept' }}</h4>
                    <p class="mb-0">Department</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Emergency Reports Section -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-fire me-2"></i>Emergency Reports
                    </h4>
                    <button class="btn btn-light btn-sm" onclick="refreshReports()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Time</th>
                                    <th>Location</th>
                                    <th>Severity</th>
                                    <th>Reporter</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="reportsTableBody">
                                {% for report in emergency_reports %}
                                <tr class="{% if report.severity == 'critical' %}table-danger{% elif report.severity == 'high' %}table-warning{% endif %}">
                                    <td>
                                        <small>{{ report.reported_at }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ report.location }}</strong>
                                        {% if report.description %}
                                        <br><small class="text-muted">{{ report.description[:50] }}...</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if report.severity == 'critical' %}danger{% elif report.severity == 'high' %}warning{% elif report.severity == 'medium' %}info{% else %}secondary{% endif %}">
                                            {{ report.severity.upper() }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ report.reporter_name or 'Anonymous' }}
                                        {% if report.reporter_phone %}
                                        <br><small>{{ report.reporter_phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if report.status == 'reported' %}danger{% elif report.status == 'responding' %}warning{% elif report.status == 'resolved' %}success{% else %}secondary{% endif %}">
                                            {{ report.status.upper() }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if report.status == 'reported' %}
                                            <button class="btn btn-warning" onclick="updateReportStatus({{ report.id }}, 'responding')">
                                                <i class="fas fa-play"></i> Respond
                                            </button>
                                            {% elif report.status == 'responding' %}
                                            <button class="btn btn-success" onclick="updateReportStatus({{ report.id }}, 'resolved')">
                                                <i class="fas fa-check"></i> Resolve
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-info" onclick="viewReportDetails({{ report.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Resources -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-danger" onclick="broadcastAlert()">
                            <i class="fas fa-broadcast-tower me-2"></i>Broadcast Alert
                        </button>
                        <button class="btn btn-warning" onclick="requestBackup()">
                            <i class="fas fa-users me-2"></i>Request Backup
                        </button>
                        <a href="{{ url_for('map_page') }}" class="btn btn-info">
                            <i class="fas fa-map-marked-alt me-2"></i>View Emergency Map
                        </a>
                        <button class="btn btn-success" onclick="generateReport()">
                            <i class="fas fa-file-alt me-2"></i>Generate Report
                        </button>
                    </div>
                </div>
            </div>

            <!-- Emergency Contacts -->
            <div class="card mt-3">
                <div class="card-header bg-secondary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-phone me-2"></i>Emergency Contacts
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between">
                            <span>Police Emergency</span>
                            <strong>117</strong>
                        </div>
                        <div class="list-group-item d-flex justify-content-between">
                            <span>Medical Emergency</span>
                            <strong>119</strong>
                        </div>
                        <div class="list-group-item d-flex justify-content-between">
                            <span>Fire Command Center</span>
                            <strong>118-001</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function updateReportStatus(reportId, status) {
    if (confirm(`Are you sure you want to mark this report as ${status}?`)) {
        fetch('/update-report-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                report_id: reportId,
                status: status,
                department_id: {{ current_user.id }}
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating report status');
            }
        })
        .catch(error => {
            alert('Error updating report status');
        });
    }
}

function refreshReports() {
    location.reload();
}

function viewReportDetails(reportId) {
    // Implement report details modal or page
    alert('Report details feature coming soon!');
}

function broadcastAlert() {
    const message = prompt('Enter alert message to broadcast:');
    if (message) {
        alert('Alert broadcast feature coming soon!');
    }
}

function requestBackup() {
    if (confirm('Request backup from other fire departments?')) {
        alert('Backup request feature coming soon!');
    }
}

function generateReport() {
    alert('Report generation feature coming soon!');
}

// Auto-refresh every 30 seconds
setInterval(function() {
    refreshReports();
}, 30000);
</script>
{% endblock %}
