#!/usr/bin/env python3
"""
Deploy <PERSON> to VPS - Emergency Response App CI/CD
Target: ***********:8080
"""

import paramiko
import os
import time

# VPS Configuration
VPS_HOST = "***********"
VPS_USER = "root"
VPS_PASSWORD = "Software-2025"
VPS_HOSTNAME = "srv878357.hstgr.cloud"
JENKINS_PORT = 8080

class JenkinsDeployer:
    def __init__(self):
        self.ssh = None
        self.sftp = None
        
    def connect(self):
        """Connect to VPS via SSH"""
        try:
            print(f"🔌 Connecting to VPS: {VPS_HOST}")
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            self.ssh.connect(
                hostname=VPS_HOST,
                username=VPS_USER,
                password=VPS_PASSWORD,
                timeout=30
            )
            
            self.sftp = self.ssh.open_sftp()
            print("✅ Connected successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def execute_command(self, command, description="", timeout=300):
        """Execute command on VPS"""
        if description:
            print(f"\n🔧 {description}")
        print(f"   Command: {command}")
        
        try:
            stdin, stdout, stderr = self.ssh.exec_command(command, timeout=timeout)
            exit_code = stdout.channel.recv_exit_status()
            
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if exit_code == 0:
                print(f"   ✅ Success")
                if output.strip():
                    print(f"   Output: {output.strip()[:200]}...")
            else:
                print(f"   ❌ Failed (exit code: {exit_code})")
                if error.strip():
                    print(f"   Error: {error.strip()}")
                    
            return exit_code == 0, output, error
            
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False, "", str(e)
    
    def deploy_jenkins(self):
        """Deploy Jenkins with Docker"""
        print("🚀 JENKINS DEPLOYMENT TO VPS")
        print("="*60)
        print(f"Target: {VPS_HOST}:{JENKINS_PORT}")
        print(f"Hostname: {VPS_HOSTNAME}")
        print("="*60)
        
        if not self.connect():
            return False
        
        try:
            # Step 1: Create Jenkins directory and configuration
            print("\n📍 Step 1: Setting up Jenkins directories...")
            self.execute_command("mkdir -p /opt/jenkins", "Creating Jenkins directory")
            self.execute_command("cd /opt/jenkins", "Changing to Jenkins directory")
            
            # Step 2: Create Docker Compose file for Jenkins
            print("\n📍 Step 2: Creating Jenkins Docker Compose configuration...")
            jenkins_compose = f"""version: '3.8'

services:
  jenkins:
    image: jenkins/jenkins:lts
    container_name: jenkins-emergency-app
    restart: unless-stopped
    ports:
      - "{JENKINS_PORT}:8080"
      - "50000:50000"
    volumes:
      - jenkins_home:/var/jenkins_home
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker:ro
    environment:
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false -Xmx2g
      - JENKINS_OPTS=--httpPort=8080
    user: root
    networks:
      - jenkins-network

  # Jenkins agent for builds
  jenkins-agent:
    image: jenkins/inbound-agent:latest
    container_name: jenkins-agent-emergency
    restart: unless-stopped
    environment:
      - JENKINS_URL=http://jenkins:8080
      - JENKINS_AGENT_NAME=emergency-agent
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker:ro
    networks:
      - jenkins-network
    depends_on:
      - jenkins

volumes:
  jenkins_home:
    driver: local

networks:
  jenkins-network:
    driver: bridge"""
            
            self.execute_command(f"cat > /opt/jenkins/docker-compose.yml << 'EOF'\n{jenkins_compose}\nEOF", "Creating Docker Compose file")
            
            # Step 3: Create Jenkins initial configuration
            print("\n📍 Step 3: Creating Jenkins initial configuration...")
            jenkins_config = """jenkins:
  systemMessage: "Emergency Response App CI/CD Server"
  numExecutors: 2
  mode: NORMAL
  scmCheckoutRetryCount: 3
  
security:
  globalJobDslSecurityConfiguration:
    useScriptSecurity: false

unclassified:
  location:
    adminAddress: "<EMAIL>"
    url: "http://***********:8080/"
    
  gitHubPluginConfig:
    hookUrl: "http://***********:8080/github-webhook/"
    
  prometheusConfiguration:
    collectingMetricsPeriodInSeconds: 120
    defaultNamespace: "jenkins"
    path: "prometheus"
    
tool:
  git:
    installations:
    - name: "Default"
      home: "/usr/bin/git"
      
  dockerTool:
    installations:
    - name: "docker"
      home: "/usr/bin/docker"
      
jobs:
  - script: |
      pipelineJob('emergency-response-app-pipeline') {
        definition {
          cpsScm {
            scm {
              git {
                remote {
                  url('https://github.com/your-username/emergency-response-app.git')
                }
                branch('*/main')
              }
            }
            scriptPath('Jenkinsfile')
          }
        }
        triggers {
          githubPush()
        }
      }"""
            
            self.execute_command(f"mkdir -p /opt/jenkins/jenkins_config", "Creating config directory")
            self.execute_command(f"cat > /opt/jenkins/jenkins_config/jenkins.yaml << 'EOF'\n{jenkins_config}\nEOF", "Creating Jenkins configuration")
            
            # Step 4: Create Jenkins plugins list
            print("\n📍 Step 4: Creating Jenkins plugins configuration...")
            plugins_list = """blueocean:1.25.2
pipeline-stage-view:2.25
docker-workflow:1.29
github:1.34.3
gitlab-plugin:1.5.35
email-ext:2.93
htmlpublisher:1.31
ssh-agent:295.v9ca_a_1c7cc3a_a_
prometheus:2.0.10
build-timeout:1.27
timestamper:1.17
pipeline-utility-steps:2.13.2
workflow-aggregator:2.6
git:4.8.3
configuration-as-code:1.55
job-dsl:1.81
credentials:2.6.1
ssh-credentials:1.19
docker-commons:1.19
docker-build-step:2.8
matrix-auth:3.1.5
role-strategy:3.2.0
build-pipeline-plugin:1.5.8
delivery-pipeline-plugin:1.5.0
parameterized-trigger:2.43
conditional-buildstep:1.4.2
copyartifact:1.46.4
junit:1.60
jacoco:3.3.2
cobertura:1.16
warnings-ng:9.12.0
checkstyle:4.0.0
pmd:4.0.0
findbugs:5.0.0
analysis-core:1.95
slack:2.48
build-name-setter:2.2.0
build-user-vars-plugin:1.8
environment-script:1.2.5"""
            
            self.execute_command(f"cat > /opt/jenkins/plugins.txt << 'EOF'\n{plugins_list}\nEOF", "Creating plugins list")
            
            # Step 5: Update firewall to allow Jenkins port
            print("\n📍 Step 5: Configuring firewall for Jenkins...")
            self.execute_command(f"ufw allow {JENKINS_PORT}", f"Opening port {JENKINS_PORT}")
            self.execute_command("ufw allow 50000", "Opening Jenkins agent port 50000")
            
            # Step 6: Start Jenkins with Docker Compose
            print("\n📍 Step 6: Starting Jenkins services...")
            self.execute_command("cd /opt/jenkins && docker-compose up -d", "Starting Jenkins with Docker Compose", timeout=600)
            
            # Step 7: Wait for Jenkins to start and get initial password
            print("\n📍 Step 7: Waiting for Jenkins to initialize...")
            self.execute_command("sleep 60", "Waiting for Jenkins to start")
            
            # Check if Jenkins is running
            success, output, error = self.execute_command("docker ps | grep jenkins", "Checking Jenkins container")
            if success:
                print("   ✅ Jenkins container is running")
            
            # Get initial admin password
            print("\n📍 Step 8: Retrieving Jenkins initial admin password...")
            success, password, error = self.execute_command("docker exec jenkins-emergency-app cat /var/jenkins_home/secrets/initialAdminPassword", "Getting initial admin password")
            
            if success and password.strip():
                initial_password = password.strip()
                print(f"   🔑 Initial Admin Password: {initial_password}")
            else:
                print("   ⚠️  Could not retrieve initial password, will try again later")
                initial_password = "Check container logs"
            
            # Step 9: Configure nginx proxy for Jenkins (optional)
            print("\n📍 Step 9: Configuring Nginx proxy for Jenkins...")
            nginx_jenkins_config = f"""# Jenkins proxy configuration
upstream jenkins {{
    server 127.0.0.1:{JENKINS_PORT};
}}

server {{
    listen 80;
    server_name jenkins.{VPS_HOSTNAME} jenkins.srv878357.hstgr.cloud;

    location / {{
        proxy_pass http://jenkins;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        
        # Jenkins specific headers
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_redirect http:// https://;
        
        # Increase timeouts for Jenkins
        proxy_connect_timeout 150;
        proxy_send_timeout 100;
        proxy_read_timeout 100;
        
        # Required for Jenkins websocket agents
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }}
}}"""
            
            self.execute_command(f"cat > /etc/nginx/sites-available/jenkins << 'EOF'\n{nginx_jenkins_config}\nEOF", "Creating Jenkins nginx config")
            self.execute_command("ln -sf /etc/nginx/sites-available/jenkins /etc/nginx/sites-enabled/", "Enabling Jenkins nginx site")
            self.execute_command("nginx -t", "Testing nginx configuration")
            self.execute_command("systemctl reload nginx", "Reloading nginx")
            
            # Step 10: Verify Jenkins is accessible
            print("\n📍 Step 10: Verifying Jenkins deployment...")
            self.execute_command("sleep 30", "Waiting for services to stabilize")
            
            success, output, error = self.execute_command(f"curl -s -o /dev/null -w '%{{http_code}}' http://localhost:{JENKINS_PORT}", "Testing Jenkins accessibility")
            if success and "200" in output:
                print("   ✅ Jenkins is accessible!")
            else:
                print("   ⚠️  Jenkins may still be starting up")
            
            # Final summary
            print("\n" + "="*60)
            print("🎉 JENKINS DEPLOYMENT COMPLETED!")
            print("="*60)
            print(f"🌐 Jenkins URL: http://{VPS_HOST}:{JENKINS_PORT}")
            print(f"🌐 Jenkins URL: http://{VPS_HOSTNAME}:{JENKINS_PORT}")
            print(f"🌐 Nginx Proxy: http://jenkins.{VPS_HOSTNAME}")
            print("="*60)
            print("🔑 JENKINS LOGIN INFORMATION:")
            print(f"   Username: admin")
            print(f"   Initial Password: {initial_password}")
            print("="*60)
            print("📋 NEXT STEPS:")
            print("1. Access Jenkins web interface")
            print("2. Complete initial setup wizard")
            print("3. Install suggested plugins")
            print("4. Create admin user")
            print("5. Configure GitHub/GitLab integration")
            print("6. Create pipeline job for Emergency Response App")
            print("="*60)
            print("🔧 MANAGEMENT COMMANDS:")
            print("   Start: cd /opt/jenkins && docker-compose up -d")
            print("   Stop: cd /opt/jenkins && docker-compose down")
            print("   Logs: docker logs jenkins-emergency-app")
            print("   Restart: docker restart jenkins-emergency-app")
            print("="*60)
            
            return True
            
        except Exception as e:
            print(f"❌ Jenkins deployment failed: {e}")
            return False
        
        finally:
            if self.ssh:
                self.ssh.close()
            if self.sftp:
                self.sftp.close()

def main():
    """Main function"""
    print("🔧 Jenkins Deployment Tool for Emergency Response App")
    print("This will deploy Jenkins CI/CD server to your VPS.")
    print(f"Target: {VPS_HOST}:{JENKINS_PORT}")
    print()
    
    deployer = JenkinsDeployer()
    success = deployer.deploy_jenkins()
    
    if success:
        print("\n🎊 Jenkins deployment completed successfully!")
        print("Your CI/CD server is now running!")
        print(f"\n🌍 Access Jenkins at:")
        print(f"   http://{VPS_HOST}:{JENKINS_PORT}")
        print(f"   http://{VPS_HOSTNAME}:{JENKINS_PORT}")
    else:
        print("\n💥 Jenkins deployment failed!")
        print("Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    main()
