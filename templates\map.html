{% extends "base.html" %}

{% block title %}Emergency Map - Emergency Response App{% endblock %}

{% block extra_head %}
<!-- Leaflet CSS for OpenStreetMap -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<!-- Leaflet Polyline Decorator for arrows -->
<script src="https://cdn.jsdelivr.net/npm/leaflet-polylinedecorator@1.6.0/dist/leaflet.polylineDecorator.js"></script>
<!-- Custom Map Styles -->
<style>
.map-container {
    position: relative;
}

.map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.emergency-marker {
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.fire-marker { background-color: #dc3545; }
.hospital-marker { background-color: #17a2b8; }
.station-marker { background-color: #ffc107; }
.user-marker { background-color: #28a745; }

.user-location-marker {
    background-color: #28a745;
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse-location 2s infinite;
}

@keyframes pulse-location {
    0% {
        box-shadow: 0 2px 8px rgba(0,0,0,0.3), 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 2px 8px rgba(0,0,0,0.3), 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 2px 8px rgba(0,0,0,0.3), 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.route-marker {
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.start-marker {
    background-color: #28a745;
}

.end-marker {
    background-color: #dc3545;
}

.pathfinding-controls {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 10px;
    margin: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.algorithm-info {
    font-size: 0.9em;
    color: #666;
    font-style: italic;
}

/* Enhanced Emergency Reporting Styles */
.media-preview-item {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 5px;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 30px;
    bottom: -20px;
    width: 2px;
    background-color: #dee2e6;
}

.timeline-item:last-child::before {
    display: none;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: white;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-item.completed .timeline-content {
    border-left-color: #28a745;
}

.timeline-item.pending .timeline-content {
    border-left-color: #6c757d;
    opacity: 0.7;
}

/* Voice recording animation */
@keyframes pulse-recording {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.btn-danger .fas.fa-microphone,
.btn-danger .fas.fa-stop {
    animation: pulse-recording 1s infinite;
}

/* Severity badges */
.badge.bg-low { background-color: #28a745 !important; }
.badge.bg-medium { background-color: #ffc107 !important; color: #000; }
.badge.bg-high { background-color: #fd7e14 !important; }
.badge.bg-critical { background-color: #dc3545 !important; }

/* Language selector in modal */
.modal-header .form-select {
    width: auto;
    min-width: 120px;
}

.leaflet-popup-content {
    margin: 8px 12px;
    line-height: 1.4;
}

.popup-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.popup-details {
    font-size: 0.9em;
    color: #666;
}

.popup-actions {
    margin-top: 10px;
    text-align: center;
}

.popup-actions .btn {
    margin: 2px;
    padding: 4px 8px;
    font-size: 0.8em;
}

/* Distance label styles for arrows */
.distance-label {
    pointer-events: none;
    z-index: 1000;
}

.distance-label div {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    line-height: 1.2;
}

/* Arrow animation for nearest location */
@keyframes arrow-pulse {
    0% { opacity: 0.8; }
    50% { opacity: 1.0; }
    100% { opacity: 0.8; }
}

.nearest-arrow {
    animation: arrow-pulse 2s infinite;
}

/* Enhanced arrow styles */
.leaflet-interactive {
    cursor: pointer;
}

.leaflet-interactive:hover {
    opacity: 0.9;
}
</style>
{% endblock %}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="{{ url_for('landing') }}">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>

        <div class="navbar-nav ms-auto">
            <a class="nav-link" href="{{ url_for('landing') }}">
                <i class="fas fa-home me-1"></i>Home
            </a>
        </div>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-5 text-primary">
                    <i class="fas fa-map-marked-alt me-3"></i>Emergency Map
                </h1>
                <p class="lead">Real-time emergency locations and nearby resources</p>
            </div>
        </div>
    </div>

    <!-- Map Controls -->
    <div class="row mb-3">
        <div class="col-md-8">
            <div class="input-group">
                <input type="text" class="form-control" id="searchLocation" placeholder="Search for location...">
                <button class="btn btn-primary" onclick="searchLocation()">
                    <i class="fas fa-search"></i>
                </button>
                <button class="btn btn-success" onclick="getCurrentLocation()">
                    <i class="fas fa-crosshairs"></i> My Location
                </button>
            </div>
        </div>
        <div class="col-md-4">
            <div class="btn-group w-100" role="group">
                <button class="btn btn-outline-danger" onclick="toggleLayer('fires')">
                    <i class="fas fa-fire"></i> Fires
                </button>
                <button class="btn btn-outline-info" onclick="toggleLayer('hospitals')">
                    <i class="fas fa-hospital"></i> Hospitals
                </button>
                <button class="btn btn-outline-warning" onclick="toggleLayer('stations')">
                    <i class="fas fa-truck"></i> Fire Stations
                </button>
            </div>
        </div>
    </div>

    <!-- Map Container -->
    <div class="row">
        <div class="col-lg-9">
            <div style="margin-bottom: 10px; padding: 10px;">
                <button class="btn btn-primary" onclick="testMap()">Test Map</button>
                <button class="btn btn-secondary" onclick="alert('JavaScript is working!')">Test JS</button>
                <button class="btn btn-info" onclick="console.log('Leaflet loaded:', typeof L !== 'undefined')">Check Leaflet</button>
            </div>
            <div id="map" style="height: 500px; width: 100%; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); background-color: #f0f0f0; border: 2px solid #ccc;">
                <div id="map-loading" style="padding: 20px; text-align: center; color: #666;">
                    <i class="fas fa-spinner fa-spin"></i> Loading map...
                    <br><br>
                    <small>If this message persists, check the browser console for errors.</small>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Legend -->
            <div class="card mb-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Map Legend</h6>
                </div>
                <div class="card-body">
                    <div class="legend-item mb-2">
                        <i class="fas fa-fire text-danger me-2"></i>Active Fires
                    </div>
                    <div class="legend-item mb-2">
                        <i class="fas fa-hospital text-info me-2"></i>Hospitals
                    </div>
                    <div class="legend-item mb-2">
                        <i class="fas fa-truck text-warning me-2"></i>Fire Stations
                    </div>
                    <div class="legend-item mb-2">
                        <i class="fas fa-map-marker-alt text-success me-2"></i>Your Location
                    </div>
                </div>
            </div>

            <!-- Pathfinding Controls -->
            <div class="card mb-3">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-route me-2"></i>Smart Pathfinding</h6>
                </div>
                <div class="card-body">
                    <p class="small algorithm-info">Using Dijkstra's algorithm for optimal routes</p>

                    <div class="mb-3">
                        <label class="form-label small">Find Hospitals by:</label>
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-info btn-sm" onclick="findOptimalHospitals('distance')">
                                <i class="fas fa-ruler me-1"></i>Distance
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="findOptimalHospitals('emergency')">
                                <i class="fas fa-ambulance me-1"></i>Emergency
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label small">Find Fire Stations by:</label>
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-warning btn-sm" onclick="findOptimalStations('distance')">
                                <i class="fas fa-ruler me-1"></i>Distance
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="findOptimalStations('vehicles')">
                                <i class="fas fa-truck me-1"></i>Vehicles
                            </button>
                        </div>
                    </div>

                    <div class="text-center">
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearAllRoutes()">
                            <i class="fas fa-times me-1"></i>Clear Arrows
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="showGraphInfo()">
                            <i class="fas fa-info-circle me-1"></i>Graph Info
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Reports -->
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Recent Reports</h6>
                </div>
                <div class="card-body">
                    <div class="report-item mb-3 p-2 border-start border-danger border-3">
                        <small class="text-muted">15 minutes ago</small>
                        <p class="mb-1 fw-bold">House Fire</p>
                        <small>Bastos, Yaoundé</small>
                    </div>
                    <div class="report-item mb-3 p-2 border-start border-warning border-3">
                        <small class="text-muted">32 minutes ago</small>
                        <p class="mb-1 fw-bold">Vehicle Fire</p>
                        <small>Douala Port Area</small>
                    </div>
                    <div class="report-item mb-3 p-2 border-start border-info border-3">
                        <small class="text-muted">1 hour ago</small>
                        <p class="mb-1 fw-bold">Brush Fire</p>
                        <small>Bamenda Hills</small>
                    </div>
                    <div class="text-center">
                        <button class="btn btn-sm btn-outline-primary">View All Reports</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5>Quick Actions</h5>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-danger w-100" onclick="reportEmergencyHere()">
                                <i class="fas fa-plus me-2"></i>Report Emergency Here
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-info w-100" onclick="findNearestHospital()">
                                <i class="fas fa-hospital me-2"></i>Nearest Hospital
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-warning w-100" onclick="findFireStation()">
                                <i class="fas fa-truck me-2"></i>Fire Station
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-success w-100" onclick="shareLocation()">
                                <i class="fas fa-share-alt me-2"></i>Share Location
                            </button>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="viewMyReports()">
                                <i class="fas fa-list me-2"></i>My Reports
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-secondary w-100" onclick="clearRoute()">
                                <i class="fas fa-times me-2"></i>Clear Route
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Leaflet JavaScript for OpenStreetMap -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<script>
// Global variables for map
let mapInstance;
let mapUserLocation = null;
let mapEmergencyMarkers = [];
let mapHospitalMarkers = [];
let mapStationMarkers = [];
let mapUserMarker = null;
let mapRouteArrows = [];
let mapDistanceLabels = [];

// Cameroon coordinates (centered on Yaoundé)
const CAMEROON_CENTER = [3.8480, 11.5021]; // [lat, lng] for Leaflet
const CAMEROON_BOUNDS = [
    [1.6, 8.5],   // Southwest [lat, lng]
    [13.1, 16.2]  // Northeast [lat, lng]
];

// Sample emergency data for Cameroon
const emergencyData = [
    {
        id: 1,
        type: 'fire',
        title: 'House Fire',
        location: 'Bastos, Yaoundé',
        coordinates: [3.8691, 11.5174], // [lat, lng] for Leaflet
        severity: 'high',
        time: '15 minutes ago',
        description: 'Residential fire in Bastos neighborhood',
        status: 'active'
    },
    {
        id: 2,
        type: 'fire',
        title: 'Vehicle Fire',
        location: 'Douala Port',
        coordinates: [4.0511, 9.7679], // [lat, lng] for Leaflet
        severity: 'medium',
        time: '32 minutes ago',
        description: 'Vehicle fire near port area',
        status: 'responding'
    },
    {
        id: 3,
        type: 'fire',
        title: 'Brush Fire',
        location: 'Bamenda Hills',
        coordinates: [5.9631, 10.1591], // [lat, lng] for Leaflet
        severity: 'low',
        time: '1 hour ago',
        description: 'Small brush fire on hillside',
        status: 'contained'
    }
];

// Comprehensive hospital data for Cameroon
const hospitalData = [
    // Yaoundé Hospitals
    {
        id: 'h001',
        name: 'Yaoundé Central Hospital (Hôpital Central)',
        coordinates: [3.8634, 11.5167],
        type: 'Public General Hospital',
        emergency: true,
        phone: '+237222234567',
        services: ['Emergency', 'Surgery', 'ICU', 'Cardiology', 'Pediatrics'],
        capacity: 500,
        rating: 4.2,
        city: 'Yaoundé',
        region: 'Centre'
    },
    {
        id: 'h002',
        name: 'Yaoundé University Teaching Hospital (CHU)',
        coordinates: [3.8480, 11.5021],
        type: 'Public Teaching Hospital',
        emergency: true,
        phone: '+237222234568',
        services: ['Emergency', 'Surgery', 'ICU', 'Oncology', 'Neurology'],
        capacity: 800,
        rating: 4.5,
        city: 'Yaoundé',
        region: 'Centre'
    },
    {
        id: 'h003',
        name: 'Yaoundé Gyneco-Obstetric Hospital',
        coordinates: [3.8691, 11.5174],
        type: 'Specialized Hospital',
        emergency: true,
        phone: '+237222234569',
        services: ['Emergency', 'Maternity', 'Gynecology', 'Pediatrics'],
        capacity: 300,
        rating: 4.0,
        city: 'Yaoundé',
        region: 'Centre'
    },
    {
        id: 'h004',
        name: 'Clinique Pasteur Yaoundé',
        coordinates: [3.8580, 11.5120],
        type: 'Private Clinic',
        emergency: true,
        phone: '+237222234570',
        services: ['Emergency', 'Surgery', 'Cardiology', 'Dermatology'],
        capacity: 150,
        rating: 4.3,
        city: 'Yaoundé',
        region: 'Centre'
    },
    {
        id: 'h005',
        name: 'Polyclinique Bonanjo',
        coordinates: [3.8720, 11.5200],
        type: 'Private Polyclinic',
        emergency: true,
        phone: '+237222234571',
        services: ['Emergency', 'General Medicine', 'Laboratory'],
        capacity: 80,
        rating: 3.8,
        city: 'Yaoundé',
        region: 'Centre'
    },

    // Douala Hospitals
    {
        id: 'h006',
        name: 'Douala General Hospital (Hôpital Général)',
        coordinates: [4.0435, 9.7043],
        type: 'Public General Hospital',
        emergency: true,
        phone: '+237233345678',
        services: ['Emergency', 'Surgery', 'ICU', 'Cardiology', 'Pediatrics'],
        capacity: 600,
        rating: 4.1,
        city: 'Douala',
        region: 'Littoral'
    },
    {
        id: 'h007',
        name: 'Douala Laquintinie Hospital',
        coordinates: [4.0511, 9.7679],
        type: 'Public Hospital',
        emergency: true,
        phone: '+237233345679',
        services: ['Emergency', 'Surgery', 'Maternity', 'Pediatrics'],
        capacity: 400,
        rating: 3.9,
        city: 'Douala',
        region: 'Littoral'
    },
    {
        id: 'h008',
        name: 'Clinique des Spécialités Douala',
        coordinates: [4.0380, 9.7100],
        type: 'Private Specialty Clinic',
        emergency: true,
        phone: '+237233345680',
        services: ['Emergency', 'Cardiology', 'Neurology', 'Orthopedics'],
        capacity: 120,
        rating: 4.4,
        city: 'Douala',
        region: 'Littoral'
    },
    {
        id: 'h009',
        name: 'Hôpital Sainte Thérèse Douala',
        coordinates: [4.0600, 9.7500],
        type: 'Private Catholic Hospital',
        emergency: true,
        phone: '+237233345681',
        services: ['Emergency', 'Surgery', 'Maternity', 'General Medicine'],
        capacity: 200,
        rating: 4.2,
        city: 'Douala',
        region: 'Littoral'
    },

    // Bamenda Hospitals
    {
        id: 'h010',
        name: 'Bamenda Regional Hospital',
        coordinates: [5.9597, 10.1463],
        type: 'Public Regional Hospital',
        emergency: true,
        phone: '+237233456789',
        services: ['Emergency', 'Surgery', 'ICU', 'Pediatrics'],
        capacity: 350,
        rating: 3.8,
        city: 'Bamenda',
        region: 'North West'
    },
    {
        id: 'h011',
        name: 'Bamenda Provincial Hospital',
        coordinates: [5.9631, 10.1591],
        type: 'Public Provincial Hospital',
        emergency: true,
        phone: '+237233456790',
        services: ['Emergency', 'General Medicine', 'Maternity'],
        capacity: 250,
        rating: 3.6,
        city: 'Bamenda',
        region: 'North West'
    },
    {
        id: 'h012',
        name: 'Mezam Polyclinic Bamenda',
        coordinates: [5.9550, 10.1400],
        type: 'Private Polyclinic',
        emergency: true,
        phone: '+237233456791',
        services: ['Emergency', 'General Medicine', 'Laboratory'],
        capacity: 100,
        rating: 3.9,
        city: 'Bamenda',
        region: 'North West'
    },

    // Garoua Hospitals
    {
        id: 'h013',
        name: 'Garoua Regional Hospital',
        coordinates: [9.3265, 13.3981],
        type: 'Public Regional Hospital',
        emergency: true,
        phone: '+237222567890',
        services: ['Emergency', 'Surgery', 'Pediatrics', 'General Medicine'],
        capacity: 300,
        rating: 3.7,
        city: 'Garoua',
        region: 'North'
    },
    {
        id: 'h014',
        name: 'Garoua Provincial Hospital',
        coordinates: [9.3200, 13.4000],
        type: 'Public Provincial Hospital',
        emergency: true,
        phone: '+237222567891',
        services: ['Emergency', 'General Medicine', 'Maternity'],
        capacity: 200,
        rating: 3.5,
        city: 'Garoua',
        region: 'North'
    },

    // Bafoussam Hospitals
    {
        id: 'h015',
        name: 'Bafoussam Regional Hospital',
        coordinates: [5.4781, 10.4199],
        type: 'Public Regional Hospital',
        emergency: true,
        phone: '+237233678901',
        services: ['Emergency', 'Surgery', 'Pediatrics'],
        capacity: 280,
        rating: 3.8,
        city: 'Bafoussam',
        region: 'West'
    },

    // Maroua Hospitals
    {
        id: 'h016',
        name: 'Maroua Regional Hospital',
        coordinates: [10.5913, 14.3153],
        type: 'Public Regional Hospital',
        emergency: true,
        phone: '+237222789012',
        services: ['Emergency', 'Surgery', 'General Medicine'],
        capacity: 250,
        rating: 3.6,
        city: 'Maroua',
        region: 'Far North'
    },

    // Bertoua Hospitals
    {
        id: 'h017',
        name: 'Bertoua Regional Hospital',
        coordinates: [4.5774, 13.6848],
        type: 'Public Regional Hospital',
        emergency: true,
        phone: '+237222890123',
        services: ['Emergency', 'General Medicine', 'Maternity'],
        capacity: 200,
        rating: 3.4,
        city: 'Bertoua',
        region: 'East'
    },

    // Ebolowa Hospitals
    {
        id: 'h018',
        name: 'Ebolowa Regional Hospital',
        coordinates: [2.9156, 11.1543],
        type: 'Public Regional Hospital',
        emergency: true,
        phone: '+237222901234',
        services: ['Emergency', 'Surgery', 'Pediatrics'],
        capacity: 220,
        rating: 3.7,
        city: 'Ebolowa',
        region: 'South'
    },

    // Ngaoundéré Hospitals
    {
        id: 'h019',
        name: 'Ngaoundéré Regional Hospital',
        coordinates: [7.3167, 13.5833],
        type: 'Public Regional Hospital',
        emergency: true,
        phone: '+237222012345',
        services: ['Emergency', 'Surgery', 'General Medicine'],
        capacity: 270,
        rating: 3.8,
        city: 'Ngaoundéré',
        region: 'Adamawa'
    },

    // Kribi Hospitals
    {
        id: 'h020',
        name: 'Kribi District Hospital',
        coordinates: [2.9373, 9.9073],
        type: 'Public District Hospital',
        emergency: true,
        phone: '+237233123456',
        services: ['Emergency', 'General Medicine', 'Maternity'],
        capacity: 150,
        rating: 3.5,
        city: 'Kribi',
        region: 'South'
    }
];

// Comprehensive fire station data for Cameroon
const stationData = [
    // Yaoundé Fire Stations
    {
        id: 'fs001',
        name: 'Yaoundé Central Fire Station',
        coordinates: [3.8480, 11.5021],
        type: 'Main Headquarters',
        phone: '+237118001',
        vehicles: 12,
        personnel: 45,
        equipment: ['Fire Trucks', 'Ambulances', 'Rescue Equipment', 'Hazmat Units'],
        responseTime: 8,
        coverage: 15,
        city: 'Yaoundé',
        region: 'Centre',
        operational: true
    },
    {
        id: 'fs002',
        name: 'Yaoundé Bastos Fire Station',
        coordinates: [3.8691, 11.5174],
        type: 'District Station',
        phone: '+237118002',
        vehicles: 6,
        personnel: 25,
        equipment: ['Fire Trucks', 'Rescue Equipment'],
        responseTime: 6,
        coverage: 10,
        city: 'Yaoundé',
        region: 'Centre',
        operational: true
    },
    {
        id: 'fs003',
        name: 'Yaoundé Mfoundi Fire Station',
        coordinates: [3.8580, 11.5120],
        type: 'District Station',
        phone: '+237118003',
        vehicles: 4,
        personnel: 20,
        equipment: ['Fire Trucks', 'First Aid'],
        responseTime: 7,
        coverage: 8,
        city: 'Yaoundé',
        region: 'Centre',
        operational: true
    },

    // Douala Fire Stations
    {
        id: 'fs004',
        name: 'Douala Port Fire Station',
        coordinates: [4.0511, 9.7679],
        type: 'Port Authority Station',
        phone: '+237118004',
        vehicles: 15,
        personnel: 60,
        equipment: ['Fire Trucks', 'Marine Rescue', 'Hazmat Units', 'Foam Units'],
        responseTime: 5,
        coverage: 20,
        city: 'Douala',
        region: 'Littoral',
        operational: true
    },
    {
        id: 'fs005',
        name: 'Douala Central Fire Station',
        coordinates: [4.0435, 9.7043],
        type: 'Main Station',
        phone: '+237118005',
        vehicles: 10,
        personnel: 40,
        equipment: ['Fire Trucks', 'Ambulances', 'Rescue Equipment'],
        responseTime: 7,
        coverage: 12,
        city: 'Douala',
        region: 'Littoral',
        operational: true
    },
    {
        id: 'fs006',
        name: 'Douala Akwa Fire Station',
        coordinates: [4.0380, 9.7100],
        type: 'District Station',
        phone: '+237118006',
        vehicles: 5,
        personnel: 22,
        equipment: ['Fire Trucks', 'First Aid'],
        responseTime: 6,
        coverage: 8,
        city: 'Douala',
        region: 'Littoral',
        operational: true
    },
    {
        id: 'fs007',
        name: 'Douala Bonaberi Fire Station',
        coordinates: [4.0600, 9.7500],
        type: 'District Station',
        phone: '+237118007',
        vehicles: 4,
        personnel: 18,
        equipment: ['Fire Trucks', 'Rescue Equipment'],
        responseTime: 8,
        coverage: 10,
        city: 'Douala',
        region: 'Littoral',
        operational: true
    },

    // Bamenda Fire Stations
    {
        id: 'fs008',
        name: 'Bamenda Regional Fire Station',
        coordinates: [5.9631, 10.1591],
        type: 'Regional Station',
        phone: '+237118008',
        vehicles: 6,
        personnel: 28,
        equipment: ['Fire Trucks', 'Rescue Equipment', 'First Aid'],
        responseTime: 10,
        coverage: 15,
        city: 'Bamenda',
        region: 'North West',
        operational: true
    },
    {
        id: 'fs009',
        name: 'Bamenda Commercial Avenue Fire Station',
        coordinates: [5.9597, 10.1463],
        type: 'District Station',
        phone: '+237118009',
        vehicles: 3,
        personnel: 15,
        equipment: ['Fire Trucks', 'First Aid'],
        responseTime: 8,
        coverage: 8,
        city: 'Bamenda',
        region: 'North West',
        operational: true
    },

    // Garoua Fire Stations
    {
        id: 'fs010',
        name: 'Garoua Regional Fire Station',
        coordinates: [9.3265, 13.3981],
        type: 'Regional Station',
        phone: '+237118010',
        vehicles: 5,
        personnel: 25,
        equipment: ['Fire Trucks', 'Rescue Equipment'],
        responseTime: 12,
        coverage: 18,
        city: 'Garoua',
        region: 'North',
        operational: true
    },

    // Bafoussam Fire Stations
    {
        id: 'fs011',
        name: 'Bafoussam Regional Fire Station',
        coordinates: [5.4781, 10.4199],
        type: 'Regional Station',
        phone: '+237118011',
        vehicles: 4,
        personnel: 20,
        equipment: ['Fire Trucks', 'Rescue Equipment'],
        responseTime: 10,
        coverage: 12,
        city: 'Bafoussam',
        region: 'West',
        operational: true
    },

    // Maroua Fire Stations
    {
        id: 'fs012',
        name: 'Maroua Regional Fire Station',
        coordinates: [10.5913, 14.3153],
        type: 'Regional Station',
        phone: '+237118012',
        vehicles: 3,
        personnel: 18,
        equipment: ['Fire Trucks', 'First Aid'],
        responseTime: 15,
        coverage: 20,
        city: 'Maroua',
        region: 'Far North',
        operational: true
    },

    // Bertoua Fire Stations
    {
        id: 'fs013',
        name: 'Bertoua Regional Fire Station',
        coordinates: [4.5774, 13.6848],
        type: 'Regional Station',
        phone: '+237118013',
        vehicles: 3,
        personnel: 15,
        equipment: ['Fire Trucks', 'First Aid'],
        responseTime: 12,
        coverage: 15,
        city: 'Bertoua',
        region: 'East',
        operational: true
    },

    // Ebolowa Fire Stations
    {
        id: 'fs014',
        name: 'Ebolowa Regional Fire Station',
        coordinates: [2.9156, 11.1543],
        type: 'Regional Station',
        phone: '+237118014',
        vehicles: 3,
        personnel: 16,
        equipment: ['Fire Trucks', 'Rescue Equipment'],
        responseTime: 11,
        coverage: 14,
        city: 'Ebolowa',
        region: 'South',
        operational: true
    },

    // Ngaoundéré Fire Stations
    {
        id: 'fs015',
        name: 'Ngaoundéré Regional Fire Station',
        coordinates: [7.3167, 13.5833],
        type: 'Regional Station',
        phone: '+237118015',
        vehicles: 4,
        personnel: 20,
        equipment: ['Fire Trucks', 'Rescue Equipment'],
        responseTime: 10,
        coverage: 16,
        city: 'Ngaoundéré',
        region: 'Adamawa',
        operational: true
    },

    // Kribi Fire Stations
    {
        id: 'fs016',
        name: 'Kribi Coastal Fire Station',
        coordinates: [2.9373, 9.9073],
        type: 'Coastal Station',
        phone: '+237118016',
        vehicles: 3,
        personnel: 14,
        equipment: ['Fire Trucks', 'Marine Rescue', 'First Aid'],
        responseTime: 9,
        coverage: 12,
        city: 'Kribi',
        region: 'South',
        operational: true
    },

    // Limbe Fire Stations
    {
        id: 'fs017',
        name: 'Limbe Fire Station',
        coordinates: [4.0186, 9.2043],
        type: 'District Station',
        phone: '+237118017',
        vehicles: 3,
        personnel: 16,
        equipment: ['Fire Trucks', 'Rescue Equipment'],
        responseTime: 8,
        coverage: 10,
        city: 'Limbe',
        region: 'South West',
        operational: true
    },

    // Buea Fire Stations
    {
        id: 'fs018',
        name: 'Buea Fire Station',
        coordinates: [4.1560, 9.2904],
        type: 'District Station',
        phone: '+237118018',
        vehicles: 2,
        personnel: 12,
        equipment: ['Fire Trucks', 'First Aid'],
        responseTime: 9,
        coverage: 8,
        city: 'Buea',
        region: 'South West',
        operational: true
    }
];



// Utility functions
function getSeverityColor(severity) {
    switch(severity) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'success';
        default: return 'secondary';
    }
}

// Distance calculation utility (Haversine formula)
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c; // Distance in kilometers
}

// Dijkstra's Algorithm Implementation for Emergency Services Pathfinding
class DijkstraPathfinder {
    constructor() {
        this.graph = new Map();
        this.distances = new Map();
        this.previous = new Map();
        this.visited = new Set();
    }

    // Build graph from hospitals and fire stations
    buildGraph(userLocation, hospitals, fireStations) {
        this.graph.clear();
        this.distances.clear();
        this.previous.clear();
        this.visited.clear();

        // Add user location as starting node
        const userNode = `user_${userLocation.lat}_${userLocation.lng}`;
        this.graph.set(userNode, new Map());

        // Add hospital nodes
        hospitals.forEach(hospital => {
            const hospitalNode = `hospital_${hospital.id}`;
            this.graph.set(hospitalNode, new Map());

            // Connect user to hospital
            const distance = calculateDistance(
                userLocation.lat, userLocation.lng,
                hospital.coordinates[0], hospital.coordinates[1]
            );
            this.graph.get(userNode).set(hospitalNode, distance);
        });

        // Add fire station nodes
        fireStations.forEach(station => {
            const stationNode = `station_${station.id}`;
            this.graph.set(stationNode, new Map());

            // Connect user to station
            const distance = calculateDistance(
                userLocation.lat, userLocation.lng,
                station.coordinates[0], station.coordinates[1]
            );
            this.graph.get(userNode).set(stationNode, distance);
        });

        // Connect hospitals to each other (for complex routing)
        hospitals.forEach(hospital1 => {
            const node1 = `hospital_${hospital1.id}`;
            hospitals.forEach(hospital2 => {
                if (hospital1.id !== hospital2.id) {
                    const node2 = `hospital_${hospital2.id}`;
                    const distance = calculateDistance(
                        hospital1.coordinates[0], hospital1.coordinates[1],
                        hospital2.coordinates[0], hospital2.coordinates[1]
                    );
                    this.graph.get(node1).set(node2, distance);
                }
            });
        });

        // Connect fire stations to each other
        fireStations.forEach(station1 => {
            const node1 = `station_${station1.id}`;
            fireStations.forEach(station2 => {
                if (station1.id !== station2.id) {
                    const node2 = `station_${station2.id}`;
                    const distance = calculateDistance(
                        station1.coordinates[0], station1.coordinates[1],
                        station2.coordinates[0], station2.coordinates[1]
                    );
                    this.graph.get(node1).set(node2, distance);
                }
            });
        });

        return userNode;
    }

    // Find shortest path using Dijkstra's algorithm
    findShortestPath(startNode, targetType = 'hospital') {
        console.log(`🔍 Running Dijkstra's algorithm from ${startNode} to find ${targetType}s`);

        // Initialize distances
        for (let node of this.graph.keys()) {
            this.distances.set(node, Infinity);
            this.previous.set(node, null);
        }
        this.distances.set(startNode, 0);
        this.visited.clear();

        // Priority queue (using array for simplicity)
        const unvisited = Array.from(this.graph.keys());
        console.log(`📊 Graph has ${unvisited.length} nodes`);

        while (unvisited.length > 0) {
            // Find node with minimum distance
            let currentNode = unvisited.reduce((min, node) =>
                this.distances.get(node) < this.distances.get(min) ? node : min
            );

            // If current node distance is infinity, we're done
            if (this.distances.get(currentNode) === Infinity) {
                break;
            }

            // Remove from unvisited
            unvisited.splice(unvisited.indexOf(currentNode), 1);
            this.visited.add(currentNode);

            console.log(`🔄 Processing node: ${currentNode}, distance: ${this.distances.get(currentNode).toFixed(2)}km`);

            // Update distances to neighbors
            const neighbors = this.graph.get(currentNode);
            if (neighbors) {
                for (let [neighbor, weight] of neighbors) {
                    if (!this.visited.has(neighbor)) {
                        const newDistance = this.distances.get(currentNode) + weight;
                        if (newDistance < this.distances.get(neighbor)) {
                            this.distances.set(neighbor, newDistance);
                            this.previous.set(neighbor, currentNode);
                            console.log(`📏 Updated ${neighbor}: ${newDistance.toFixed(2)}km`);
                        }
                    }
                }
            }
        }

        console.log('✅ Dijkstra\'s algorithm completed');
        return this.getOptimalTargets(targetType);
    }

    // Get optimal targets sorted by distance and other factors
    getOptimalTargets(targetType) {
        console.log(`🎯 Getting optimal ${targetType}s from Dijkstra results`);
        const targets = [];

        for (let [node, distance] of this.distances) {
            if (node.startsWith(targetType) && distance !== Infinity) {
                const id = node.split('_')[1];
                const data = targetType === 'hospital' ?
                    hospitalData.find(h => h.id === id) :
                    stationData.find(s => s.id === id);

                if (data) {
                    targets.push({
                        ...data,
                        distance: distance,
                        path: this.reconstructPath(node)
                    });
                    console.log(`📍 Found ${data.name}: ${distance.toFixed(2)}km`);
                }
            }
        }

        console.log(`📊 Found ${targets.length} reachable ${targetType}s`);

        // Sort by distance first (Dijkstra's shortest path), then by other criteria
        return targets.sort((a, b) => {
            // Primary sort: distance (shortest first)
            const distanceDiff = a.distance - b.distance;
            if (Math.abs(distanceDiff) > 0.1) { // 100m difference threshold
                return distanceDiff;
            }

            // Secondary sort: specific criteria for each type
            if (targetType === 'hospital') {
                // For hospitals: emergency capability, then rating
                if (a.emergency !== b.emergency) return b.emergency - a.emergency;
                return b.rating - a.rating;
            } else {
                // For fire stations: operational status, then vehicles
                if (a.operational !== b.operational) return b.operational - a.operational;
                return b.vehicles - a.vehicles;
            }
        });
    }

    // Reconstruct path from start to target
    reconstructPath(targetNode) {
        const path = [];
        let currentNode = targetNode;

        while (currentNode !== null) {
            path.unshift(currentNode);
            currentNode = this.previous.get(currentNode);
        }

        return path;
    }

    // Calculate route efficiency score
    calculateRouteEfficiency(target, userLocation) {
        const baseScore = 100;
        const distancePenalty = target.distance * 2; // 2 points per km
        const timePenalty = target.responseTime || 0; // Response time penalty

        let bonusPoints = 0;
        if (target.emergency) bonusPoints += 20;
        if (target.rating && target.rating > 4.0) bonusPoints += 10;
        if (target.vehicles && target.vehicles > 5) bonusPoints += 15;

        return Math.max(0, baseScore - distancePenalty - timePenalty + bonusPoints);
    }
}

// Global pathfinder instance
const pathfinder = new DijkstraPathfinder();





// Get current location and display on map
function getCurrentLocation() {
    if (!navigator.geolocation) {
        alert('Geolocation is not supported by this browser.');
        return;
    }

    if (!mapInstance) {
        alert('Map not initialized yet. Please wait.');
        return;
    }

    // Show loading state
    const locationBtn = document.querySelector('button[onclick="getCurrentLocation()"]');
    const originalText = locationBtn.innerHTML;
    locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
    locationBtn.disabled = true;

    navigator.geolocation.getCurrentPosition(
        function(position) {
            mapUserLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude,
                accuracy: position.coords.accuracy
            };

            console.log('Map user location obtained:', mapUserLocation);

            // Remove existing user marker if any
            if (mapUserMarker) {
                mapInstance.removeLayer(mapUserMarker);
            }

            // Create custom user location icon
            const userIcon = L.divIcon({
                className: 'user-location-marker',
                html: '<i class="fas fa-user" style="color: white; font-size: 14px;"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 15],
                popupAnchor: [0, -15]
            });

            // Add user marker to map
            mapUserMarker = L.marker([mapUserLocation.lat, mapUserLocation.lng], {
                icon: userIcon
            }).addTo(mapInstance);

            // Create popup content
            const popupContent = `
                <div class="popup-title">Your Location</div>
                <div class="popup-details">
                    <strong>Coordinates:</strong> ${mapUserLocation.lat.toFixed(6)}, ${mapUserLocation.lng.toFixed(6)}<br>
                    <strong>Accuracy:</strong> ±${Math.round(mapUserLocation.accuracy)} meters
                </div>
                <div class="popup-actions">
                    <button class="btn btn-danger btn-sm" onclick="reportEmergencyHere()">
                        <i class="fas fa-exclamation-triangle"></i> Report Emergency
                    </button>
                    <button class="btn btn-info btn-sm" onclick="shareLocation()">
                        <i class="fas fa-share-alt"></i> Share Location
                    </button>
                </div>
            `;

            mapUserMarker.bindPopup(popupContent);

            // Center map on user location
            mapInstance.setView([mapUserLocation.lat, mapUserLocation.lng], 15);

            // Open popup
            mapUserMarker.openPopup();

            // Restore button
            locationBtn.innerHTML = originalText;
            locationBtn.disabled = false;

            // Show success message
            showSuccessToast('Location found! You are now centered on the map.');

        },
        function(error) {
            console.warn('Location error:', error.message);

            // Restore button
            locationBtn.innerHTML = originalText;
            locationBtn.disabled = false;

            // Handle different error types
            let errorMessage = 'Could not get your location. ';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage += 'Please allow location access and try again.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage += 'Location information is unavailable.';
                    break;
                case error.TIMEOUT:
                    errorMessage += 'Location request timed out. Please try again.';
                    break;
                default:
                    errorMessage += 'An unknown error occurred.';
                    break;
            }

            showErrorToast(errorMessage);
        },
        {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 300000 // 5 minutes
        }
    );
}

function searchLocation() {
    const query = document.getElementById('searchLocation').value;
    if (!query) return;

    if (!mapInstance) {
        alert('Map not initialized yet. Please wait.');
        return;
    }

    // Simple search - fly to Yaoundé if searching for it
    if (query.toLowerCase().includes('yaoundé') || query.toLowerCase().includes('yaounde')) {
        mapInstance.setView([3.8480, 11.5021], 12);
    } else if (query.toLowerCase().includes('douala')) {
        mapInstance.setView([4.0511, 9.7679], 12);
    } else if (query.toLowerCase().includes('bamenda')) {
        mapInstance.setView([5.9631, 10.1591], 12);
    } else {
        alert('Try searching for Yaoundé, Douala, or Bamenda');
    }
}

// Smart pathfinding functions using Dijkstra's algorithm
function findOptimalHospitals(criteria = 'distance') {
    console.log(`🏥 Finding optimal hospitals by ${criteria}...`);

    // Check if user location is available
    if (!mapUserLocation) {
        showErrorToast('Getting your location first...');
        // Try to get location automatically
        getCurrentLocationForPathfinding(() => {
            findOptimalHospitals(criteria);
        });
        return;
    }

    console.log('User location available:', mapUserLocation);
    console.log('Building graph with hospitals...');

    // Build graph and find optimal paths using Dijkstra's algorithm
    const userNode = pathfinder.buildGraph(mapUserLocation, hospitalData, stationData);
    console.log('Graph built, finding shortest paths...');

    const optimalHospitals = pathfinder.findShortestPath(userNode, 'hospital');
    console.log('Optimal hospitals found:', optimalHospitals.length);

    // Apply additional criteria
    let sortedHospitals;
    if (criteria === 'emergency') {
        sortedHospitals = optimalHospitals.filter(h => h.emergency).slice(0, 5);
        console.log('Filtered for emergency hospitals:', sortedHospitals.length);
    } else {
        sortedHospitals = optimalHospitals.slice(0, 5);
        console.log('Top 5 hospitals by distance:', sortedHospitals.length);
    }

    if (sortedHospitals.length === 0) {
        showErrorToast('No hospitals found. Please try again.');
        return;
    }

    // Clear existing hospital markers and routes
    clearHospitalMarkers();
    clearAllRoutes();

    // Add optimal hospital markers with arrows to ALL hospitals
    sortedHospitals.forEach((hospital, index) => {
        console.log(`Adding hospital #${index + 1}: ${hospital.name} (${hospital.distance.toFixed(2)}km)`);
        addHospitalMarker(hospital, index + 1);

        // Draw arrow to each hospital with distance label
        const isNearest = index === 0;
        drawArrowWithDistance(mapUserLocation, hospital, 'hospital', isNearest);
    });

    // Show results panel
    showOptimalResultsPanel(sortedHospitals, 'hospitals', criteria);

    // Fit map to show all hospitals and user location
    if (sortedHospitals.length > 0) {
        const bounds = L.latLngBounds();
        bounds.extend([mapUserLocation.lat, mapUserLocation.lng]);
        sortedHospitals.forEach(hospital => {
            bounds.extend([hospital.coordinates[0], hospital.coordinates[1]]);
        });
        mapInstance.fitBounds(bounds, { padding: [20, 20] });
    }

    showSuccessToast(`✅ Found ${sortedHospitals.length} hospitals with arrows! Nearest: ${sortedHospitals[0].name} (${sortedHospitals[0].distance.toFixed(2)}km)`);
}

function findOptimalStations(criteria = 'distance') {
    console.log(`🚒 Finding optimal fire stations by ${criteria}...`);

    // Check if user location is available
    if (!mapUserLocation) {
        showErrorToast('Getting your location first...');
        // Try to get location automatically
        getCurrentLocationForPathfinding(() => {
            findOptimalStations(criteria);
        });
        return;
    }

    console.log('User location available:', mapUserLocation);
    console.log('Building graph with fire stations...');

    // Build graph and find optimal paths using Dijkstra's algorithm
    const userNode = pathfinder.buildGraph(mapUserLocation, hospitalData, stationData);
    console.log('Graph built, finding shortest paths...');

    const optimalStations = pathfinder.findShortestPath(userNode, 'station');
    console.log('Optimal fire stations found:', optimalStations.length);

    // Apply additional criteria
    let sortedStations;
    if (criteria === 'vehicles') {
        sortedStations = optimalStations.sort((a, b) => b.vehicles - a.vehicles).slice(0, 5);
        console.log('Sorted by vehicle count:', sortedStations.length);
    } else {
        sortedStations = optimalStations.slice(0, 5);
        console.log('Top 5 stations by distance:', sortedStations.length);
    }

    if (sortedStations.length === 0) {
        showErrorToast('No fire stations found. Please try again.');
        return;
    }

    // Clear existing station markers and routes
    clearStationMarkers();
    clearAllRoutes();

    // Add optimal station markers with arrows to ALL stations
    sortedStations.forEach((station, index) => {
        console.log(`Adding station #${index + 1}: ${station.name} (${station.distance.toFixed(2)}km)`);
        addFireStationMarker(station, index + 1);

        // Draw arrow to each station with distance label
        const isNearest = index === 0;
        drawArrowWithDistance(mapUserLocation, station, 'station', isNearest);
    });

    // Show results panel
    showOptimalResultsPanel(sortedStations, 'stations', criteria);

    // Fit map to show all stations and user location
    if (sortedStations.length > 0) {
        const bounds = L.latLngBounds();
        bounds.extend([mapUserLocation.lat, mapUserLocation.lng]);
        sortedStations.forEach(station => {
            bounds.extend([station.coordinates[0], station.coordinates[1]]);
        });
        mapInstance.fitBounds(bounds, { padding: [20, 20] });
    }

    showSuccessToast(`✅ Found ${sortedStations.length} fire stations with arrows! Nearest: ${sortedStations[0].name} (${sortedStations[0].distance.toFixed(2)}km)`);
}

function findNearestHospital() {
    findOptimalHospitals('distance');
}

function findFireStation() {
    findOptimalStations('distance');
}

// Helper function to get location automatically for pathfinding
function getCurrentLocationForPathfinding(callback) {
    if (!navigator.geolocation) {
        showErrorToast('Geolocation is not supported by this browser.');
        return;
    }

    console.log('🌍 Getting user location for pathfinding...');

    navigator.geolocation.getCurrentPosition(
        function(position) {
            mapUserLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude,
                accuracy: position.coords.accuracy
            };

            console.log('✅ Location obtained for pathfinding:', mapUserLocation);

            // Remove existing user marker if any
            if (mapUserMarker) {
                mapInstance.removeLayer(mapUserMarker);
            }

            // Create custom user location icon
            const userIcon = L.divIcon({
                className: 'user-location-marker',
                html: '<i class="fas fa-user" style="color: white; font-size: 14px;"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 15],
                popupAnchor: [0, -15]
            });

            // Add user marker to map
            mapUserMarker = L.marker([mapUserLocation.lat, mapUserLocation.lng], {
                icon: userIcon
            }).addTo(mapInstance);

            // Create popup content
            const popupContent = `
                <div class="popup-title">Your Location</div>
                <div class="popup-details">
                    <strong>Coordinates:</strong> ${mapUserLocation.lat.toFixed(6)}, ${mapUserLocation.lng.toFixed(6)}<br>
                    <strong>Accuracy:</strong> ±${Math.round(mapUserLocation.accuracy)} meters
                </div>
            `;

            mapUserMarker.bindPopup(popupContent);

            showSuccessToast('📍 Location detected! Finding optimal routes...');

            // Execute callback function
            if (callback) {
                callback();
            }
        },
        function(error) {
            console.error('❌ Location error:', error.message);

            let errorMessage = 'Could not get your location. ';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage += 'Please allow location access and try again.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage += 'Location information is unavailable.';
                    break;
                case error.TIMEOUT:
                    errorMessage += 'Location request timed out. Please try again.';
                    break;
                default:
                    errorMessage += 'Please try clicking "My Location" button first.';
                    break;
            }

            showErrorToast(errorMessage);
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        }
    );
}

// Enhanced marker functions
function addHospitalMarker(hospital, rank = null) {
    const hospitalIcon = L.divIcon({
        className: 'emergency-marker hospital-marker',
        html: `<i class="fas fa-hospital" style="color: white; font-size: 14px;"></i>${rank ? `<span style="position: absolute; top: -5px; right: -5px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;">${rank}</span>` : ''}`,
        iconSize: [30, 30],
        iconAnchor: [15, 15],
        popupAnchor: [0, -15]
    });

    const marker = L.marker([hospital.coordinates[0], hospital.coordinates[1]], {
        icon: hospitalIcon
    }).addTo(mapInstance);

    const efficiency = pathfinder.calculateRouteEfficiency(hospital, mapUserLocation);

    const popupContent = `
        <div class="popup-title">${hospital.name}</div>
        <div class="popup-details">
            <strong>Type:</strong> ${hospital.type}<br>
            <strong>Distance:</strong> ${hospital.distance ? hospital.distance.toFixed(2) + ' km' : 'Calculating...'}<br>
            <strong>Emergency:</strong> ${hospital.emergency ? '✅ Yes' : '❌ No'}<br>
            <strong>Rating:</strong> ${'⭐'.repeat(Math.floor(hospital.rating))} (${hospital.rating})<br>
            <strong>Capacity:</strong> ${hospital.capacity} beds<br>
            <strong>Services:</strong> ${hospital.services.slice(0, 3).join(', ')}${hospital.services.length > 3 ? '...' : ''}<br>
            <strong>Phone:</strong> ${hospital.phone}<br>
            <strong>Efficiency Score:</strong> ${efficiency.toFixed(1)}/100
        </div>
        <div class="popup-actions">
            <button class="btn btn-info btn-sm" onclick="getDirections(${hospital.coordinates[0]}, ${hospital.coordinates[1]}, '${hospital.name}')">
                <i class="fas fa-directions"></i> Directions
            </button>
            <button class="btn btn-success btn-sm" onclick="callHospital('${hospital.phone}')">
                <i class="fas fa-phone"></i> Call
            </button>
        </div>
    `;

    marker.bindPopup(popupContent);
    mapHospitalMarkers.push(marker);

    return marker;
}

function addFireStationMarker(station, rank = null) {
    const stationIcon = L.divIcon({
        className: 'emergency-marker station-marker',
        html: `<i class="fas fa-truck" style="color: white; font-size: 14px;"></i>${rank ? `<span style="position: absolute; top: -5px; right: -5px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;">${rank}</span>` : ''}`,
        iconSize: [30, 30],
        iconAnchor: [15, 15],
        popupAnchor: [0, -15]
    });

    const marker = L.marker([station.coordinates[0], station.coordinates[1]], {
        icon: stationIcon
    }).addTo(mapInstance);

    const efficiency = pathfinder.calculateRouteEfficiency(station, mapUserLocation);

    const popupContent = `
        <div class="popup-title">${station.name}</div>
        <div class="popup-details">
            <strong>Type:</strong> ${station.type}<br>
            <strong>Distance:</strong> ${station.distance ? station.distance.toFixed(2) + ' km' : 'Calculating...'}<br>
            <strong>Vehicles:</strong> ${station.vehicles} units<br>
            <strong>Personnel:</strong> ${station.personnel} firefighters<br>
            <strong>Response Time:</strong> ~${station.responseTime} minutes<br>
            <strong>Coverage:</strong> ${station.coverage} km radius<br>
            <strong>Equipment:</strong> ${station.equipment.slice(0, 2).join(', ')}${station.equipment.length > 2 ? '...' : ''}<br>
            <strong>Phone:</strong> ${station.phone}<br>
            <strong>Status:</strong> ${station.operational ? '🟢 Operational' : '🔴 Offline'}<br>
            <strong>Efficiency Score:</strong> ${efficiency.toFixed(1)}/100
        </div>
        <div class="popup-actions">
            <button class="btn btn-info btn-sm" onclick="getDirections(${station.coordinates[0]}, ${station.coordinates[1]}, '${station.name}')">
                <i class="fas fa-directions"></i> Directions
            </button>
            <button class="btn btn-danger btn-sm" onclick="callFireStation('${station.phone}')">
                <i class="fas fa-phone"></i> Call
            </button>
        </div>
    `;

    marker.bindPopup(popupContent);
    mapStationMarkers.push(marker);

    return marker;
}

function toggleLayer(layer) {
    console.log('Toggle layer:', layer);

    switch(layer) {
        case 'hospitals':
            if (mapHospitalMarkers.length > 0) {
                clearHospitalMarkers();
                showInfoToast('Hospital markers hidden');
            } else {
                showAllHospitals();
                showInfoToast('Hospital markers shown');
            }
            break;
        case 'stations':
            if (mapStationMarkers.length > 0) {
                clearStationMarkers();
                showInfoToast('Fire station markers hidden');
            } else {
                showAllFireStations();
                showInfoToast('Fire station markers shown');
            }
            break;
        case 'fires':
            // Toggle emergency fire markers
            showInfoToast('Emergency fire markers toggled');
            break;
    }
}

// Enhanced Emergency Reporting System
let currentLanguage = 'en'; // Default language
let speechRecognition = null;
let mediaRecorder = null;
let recordedChunks = [];
let emergencyReports = JSON.parse(localStorage.getItem('emergencyReports') || '[]');

// Multi-language support
const translations = {
    en: {
        reportEmergency: 'Report Emergency',
        selectType: 'Select the type of emergency:',
        fireEmergency: 'Fire Emergency',
        medicalEmergency: 'Medical Emergency',
        trafficAccident: 'Traffic Accident',
        crimeSecurity: 'Crime/Security',
        otherEmergency: 'Other Emergency',
        additionalDetails: 'Additional Details (Optional):',
        describeEmergency: 'Describe the emergency situation...',
        yourLocation: 'Your Location:',
        anonymous: 'Report Anonymously',
        attachMedia: 'Attach Photos/Videos',
        voiceDescription: 'Voice Description',
        startRecording: 'Start Recording',
        stopRecording: 'Stop Recording',
        severity: 'Severity:',
        low: 'Low',
        medium: 'Medium',
        high: 'High',
        critical: 'Critical',
        cancel: 'Cancel',
        reportEmergencyBtn: 'Report Emergency',
        uploading: 'Uploading...',
        processing: 'Processing...'
    },
    fr: {
        reportEmergency: 'Signaler une Urgence',
        selectType: 'Sélectionnez le type d\'urgence:',
        fireEmergency: 'Urgence Incendie',
        medicalEmergency: 'Urgence Médicale',
        trafficAccident: 'Accident de Circulation',
        crimeSecurity: 'Crime/Sécurité',
        otherEmergency: 'Autre Urgence',
        additionalDetails: 'Détails Supplémentaires (Optionnel):',
        describeEmergency: 'Décrivez la situation d\'urgence...',
        yourLocation: 'Votre Emplacement:',
        anonymous: 'Signaler Anonymement',
        attachMedia: 'Joindre Photos/Vidéos',
        voiceDescription: 'Description Vocale',
        startRecording: 'Commencer l\'Enregistrement',
        stopRecording: 'Arrêter l\'Enregistrement',
        severity: 'Gravité:',
        low: 'Faible',
        medium: 'Moyen',
        high: 'Élevé',
        critical: 'Critique',
        cancel: 'Annuler',
        reportEmergencyBtn: 'Signaler l\'Urgence',
        uploading: 'Téléchargement...',
        processing: 'Traitement...'
    }
};

// Severity assessment keywords
const severityKeywords = {
    critical: ['death', 'dying', 'unconscious', 'explosion', 'collapse', 'trapped', 'bleeding heavily', 'not breathing', 'chest pain', 'stroke', 'heart attack', 'overdose', 'suicide', 'weapon', 'shooting', 'stabbing'],
    high: ['fire', 'smoke', 'burning', 'injured', 'accident', 'crash', 'broken bones', 'severe pain', 'difficulty breathing', 'allergic reaction', 'robbery', 'assault', 'domestic violence'],
    medium: ['minor injury', 'cut', 'bruise', 'sprain', 'theft', 'vandalism', 'suspicious activity', 'lost person', 'animal attack'],
    low: ['noise complaint', 'minor dispute', 'property damage', 'lost item', 'welfare check']
};

function reportEmergencyHere() {
    if (!mapUserLocation) {
        showErrorToast(getTranslation('Please get your location first by clicking "My Location" button.'));
        return;
    }

    const emergencyTypes = [
        { value: 'fire', label: getTranslation('fireEmergency'), icon: 'fas fa-fire', color: '#dc3545' },
        { value: 'medical', label: getTranslation('medicalEmergency'), icon: 'fas fa-ambulance', color: '#28a745' },
        { value: 'accident', label: getTranslation('trafficAccident'), icon: 'fas fa-car-crash', color: '#ffc107' },
        { value: 'crime', label: getTranslation('crimeSecurity'), icon: 'fas fa-shield-alt', color: '#6f42c1' },
        { value: 'other', label: getTranslation('otherEmergency'), icon: 'fas fa-exclamation-triangle', color: '#fd7e14' }
    ];

    // Create enhanced emergency reporting modal
    const modalHtml = `
        <div class="modal fade" id="emergencyModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>${getTranslation('reportEmergency')}
                        </h5>
                        <div class="ms-auto me-3">
                            <select class="form-select form-select-sm text-dark" id="languageSelect" onchange="changeLanguage(this.value)">
                                <option value="en" ${currentLanguage === 'en' ? 'selected' : ''}>English</option>
                                <option value="fr" ${currentLanguage === 'fr' ? 'selected' : ''}>Français</option>
                            </select>
                        </div>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Location Info -->
                        <div class="alert alert-info">
                            <strong>${getTranslation('yourLocation')}</strong> ${mapUserLocation.lat.toFixed(6)}, ${mapUserLocation.lng.toFixed(6)}
                            <div class="form-check form-check-inline float-end">
                                <input class="form-check-input" type="checkbox" id="anonymousReport">
                                <label class="form-check-label" for="anonymousReport">${getTranslation('anonymous')}</label>
                            </div>
                        </div>

                        <!-- Emergency Type Selection -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">${getTranslation('selectType')}</label>
                            <div class="row">
                                ${emergencyTypes.map(type => `
                                    <div class="col-md-6 mb-2">
                                        <button class="btn btn-outline-secondary w-100 text-start emergency-type-btn"
                                                data-type="${type.value}"
                                                style="border-color: ${type.color};">
                                            <i class="${type.icon} me-2" style="color: ${type.color};"></i>
                                            ${type.label}
                                        </button>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <!-- Description Section -->
                        <div class="mb-3">
                            <label for="emergencyDescription" class="form-label fw-bold">${getTranslation('additionalDetails')}</label>
                            <div class="input-group mb-2">
                                <textarea class="form-control" id="emergencyDescription" rows="3"
                                          placeholder="${getTranslation('describeEmergency')}"></textarea>
                                <button class="btn btn-outline-primary" type="button" id="voiceRecordBtn" onclick="toggleVoiceRecording()">
                                    <i class="fas fa-microphone"></i>
                                </button>
                            </div>
                            <div id="voiceRecordingStatus" class="small text-muted"></div>
                        </div>

                        <!-- Media Upload Section -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">${getTranslation('attachMedia')}</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="file" class="form-control" id="photoUpload" accept="image/*" multiple>
                                    <small class="text-muted">Photos (max 5MB each)</small>
                                </div>
                                <div class="col-md-6">
                                    <input type="file" class="form-control" id="videoUpload" accept="video/*">
                                    <small class="text-muted">Video (max 50MB)</small>
                                </div>
                            </div>
                            <div id="mediaPreview" class="mt-2"></div>
                        </div>

                        <!-- Auto-detected Severity -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">${getTranslation('severity')}</label>
                            <div class="btn-group w-100" role="group" id="severityGroup">
                                <input type="radio" class="btn-check" name="severity" id="severityLow" value="low">
                                <label class="btn btn-outline-success" for="severityLow">${getTranslation('low')}</label>

                                <input type="radio" class="btn-check" name="severity" id="severityMedium" value="medium">
                                <label class="btn btn-outline-warning" for="severityMedium">${getTranslation('medium')}</label>

                                <input type="radio" class="btn-check" name="severity" id="severityHigh" value="high">
                                <label class="btn btn-outline-danger" for="severityHigh">${getTranslation('high')}</label>

                                <input type="radio" class="btn-check" name="severity" id="severityCritical" value="critical">
                                <label class="btn btn-outline-dark" for="severityCritical">${getTranslation('critical')}</label>
                            </div>
                            <div id="severityDetection" class="small text-muted mt-1"></div>
                        </div>

                        <!-- Progress Bar -->
                        <div id="uploadProgress" class="progress mb-3" style="display: none;">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${getTranslation('cancel')}</button>
                        <button type="button" class="btn btn-danger" id="submitEmergency" disabled>
                            <i class="fas fa-phone me-1"></i>${getTranslation('reportEmergencyBtn')}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('emergencyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('emergencyModal'));
    modal.show();

    // Initialize enhanced emergency reporting
    initializeEnhancedReporting(modal);
}

// Enhanced Emergency Reporting Functions
function initializeEnhancedReporting(modal) {
    let selectedType = null;
    let uploadedFiles = [];
    let voiceRecording = null;

    // Handle emergency type selection
    document.querySelectorAll('.emergency-type-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove previous selection
            document.querySelectorAll('.emergency-type-btn').forEach(b => {
                b.classList.remove('btn-danger');
                b.classList.add('btn-outline-secondary');
            });

            // Select current
            this.classList.remove('btn-outline-secondary');
            this.classList.add('btn-danger');
            selectedType = this.dataset.type;

            // Enable submit button if type is selected
            updateSubmitButton();
        });
    });

    // Handle description input with auto-severity detection
    const descriptionTextarea = document.getElementById('emergencyDescription');
    descriptionTextarea.addEventListener('input', function() {
        const description = this.value.toLowerCase();
        const detectedSeverity = assessSeverity(description);

        if (detectedSeverity) {
            // Auto-select severity
            document.getElementById(`severity${detectedSeverity.charAt(0).toUpperCase() + detectedSeverity.slice(1)}`).checked = true;

            // Show detection message
            document.getElementById('severityDetection').innerHTML =
                `<i class="fas fa-robot text-primary"></i> Auto-detected: ${detectedSeverity} severity`;
        }

        updateSubmitButton();
    });

    // Handle file uploads
    document.getElementById('photoUpload').addEventListener('change', handlePhotoUpload);
    document.getElementById('videoUpload').addEventListener('change', handleVideoUpload);

    // Handle emergency submission
    document.getElementById('submitEmergency').addEventListener('click', function() {
        if (!selectedType) return;

        const formData = collectEmergencyData(selectedType, uploadedFiles, voiceRecording);
        submitEnhancedEmergencyReport(formData);
        modal.hide();
    });

    function updateSubmitButton() {
        const hasType = selectedType !== null;
        const hasDescription = document.getElementById('emergencyDescription').value.trim().length > 0;
        document.getElementById('submitEmergency').disabled = !hasType;
    }
}

// Enhanced Emergency Reporting Support Functions

// Translation helper
function getTranslation(key) {
    return translations[currentLanguage][key] || key;
}

// Language change function
function changeLanguage(lang) {
    currentLanguage = lang;
    localStorage.setItem('preferredLanguage', lang);

    // Refresh modal content if open
    const modal = document.getElementById('emergencyModal');
    if (modal && modal.classList.contains('show')) {
        // Close and reopen modal with new language
        const modalInstance = bootstrap.Modal.getInstance(modal);
        modalInstance.hide();
        setTimeout(() => reportEmergencyHere(), 300);
    }
}

// Severity assessment function
function assessSeverity(description) {
    const text = description.toLowerCase();

    // Check for critical keywords
    for (let keyword of severityKeywords.critical) {
        if (text.includes(keyword)) {
            return 'critical';
        }
    }

    // Check for high severity keywords
    for (let keyword of severityKeywords.high) {
        if (text.includes(keyword)) {
            return 'high';
        }
    }

    // Check for medium severity keywords
    for (let keyword of severityKeywords.medium) {
        if (text.includes(keyword)) {
            return 'medium';
        }
    }

    // Check for low severity keywords
    for (let keyword of severityKeywords.low) {
        if (text.includes(keyword)) {
            return 'low';
        }
    }

    // Default to medium if no keywords match but description exists
    return text.length > 10 ? 'medium' : null;
}

// Voice recording functions
function toggleVoiceRecording() {
    const btn = document.getElementById('voiceRecordBtn');
    const status = document.getElementById('voiceRecordingStatus');

    if (!speechRecognition) {
        initializeSpeechRecognition();
    }

    if (speechRecognition.isRecording) {
        stopVoiceRecording();
    } else {
        startVoiceRecording();
    }
}

function initializeSpeechRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        speechRecognition = new SpeechRecognition();

        speechRecognition.continuous = true;
        speechRecognition.interimResults = true;
        speechRecognition.lang = currentLanguage === 'fr' ? 'fr-FR' : 'en-US';

        speechRecognition.onstart = function() {
            speechRecognition.isRecording = true;
            updateVoiceRecordingUI(true);
        };

        speechRecognition.onresult = function(event) {
            let finalTranscript = '';
            let interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            const textarea = document.getElementById('emergencyDescription');
            const currentText = textarea.value;
            const newText = currentText + finalTranscript;

            if (finalTranscript) {
                textarea.value = newText;
                // Trigger severity assessment
                textarea.dispatchEvent(new Event('input'));
            }

            // Show interim results
            if (interimTranscript) {
                document.getElementById('voiceRecordingStatus').innerHTML =
                    `<i class="fas fa-microphone text-danger"></i> ${getTranslation('Recording')}: "${interimTranscript}"`;
            }
        };

        speechRecognition.onerror = function(event) {
            console.error('Speech recognition error:', event.error);
            showErrorToast(`Voice recognition error: ${event.error}`);
            stopVoiceRecording();
        };

        speechRecognition.onend = function() {
            speechRecognition.isRecording = false;
            updateVoiceRecordingUI(false);
        };
    } else {
        showErrorToast('Voice recognition not supported in this browser.');
    }
}

function startVoiceRecording() {
    if (speechRecognition) {
        speechRecognition.start();
    }
}

function stopVoiceRecording() {
    if (speechRecognition && speechRecognition.isRecording) {
        speechRecognition.stop();
    }
}

function updateVoiceRecordingUI(isRecording) {
    const btn = document.getElementById('voiceRecordBtn');
    const status = document.getElementById('voiceRecordingStatus');

    if (isRecording) {
        btn.innerHTML = '<i class="fas fa-stop text-danger"></i>';
        btn.classList.remove('btn-outline-primary');
        btn.classList.add('btn-danger');
        status.innerHTML = `<i class="fas fa-microphone text-danger"></i> ${getTranslation('Recording')}...`;
    } else {
        btn.innerHTML = '<i class="fas fa-microphone"></i>';
        btn.classList.remove('btn-danger');
        btn.classList.add('btn-outline-primary');
        status.innerHTML = '';
    }
}

// Media upload functions
function handlePhotoUpload(event) {
    const files = Array.from(event.target.files);
    const maxSize = 5 * 1024 * 1024; // 5MB
    const validFiles = [];

    files.forEach(file => {
        if (file.size > maxSize) {
            showErrorToast(`Photo ${file.name} is too large. Maximum size is 5MB.`);
            return;
        }

        if (!file.type.startsWith('image/')) {
            showErrorToast(`${file.name} is not a valid image file.`);
            return;
        }

        validFiles.push(file);
    });

    if (validFiles.length > 0) {
        displayMediaPreview(validFiles, 'photo');
        showSuccessToast(`${validFiles.length} photo(s) ready for upload.`);
    }
}

function handleVideoUpload(event) {
    const file = event.target.files[0];
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (!file) return;

    if (file.size > maxSize) {
        showErrorToast('Video file is too large. Maximum size is 50MB.');
        event.target.value = '';
        return;
    }

    if (!file.type.startsWith('video/')) {
        showErrorToast('Please select a valid video file.');
        event.target.value = '';
        return;
    }

    displayMediaPreview([file], 'video');
    showSuccessToast('Video ready for upload.');
}

function displayMediaPreview(files, type) {
    const preview = document.getElementById('mediaPreview');

    files.forEach(file => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const mediaElement = document.createElement('div');
            mediaElement.className = 'media-preview-item d-inline-block me-2 mb-2 position-relative';

            if (type === 'photo') {
                mediaElement.innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                    <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0"
                            onclick="this.parentElement.remove()" style="transform: translate(50%, -50%);">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            } else {
                mediaElement.innerHTML = `
                    <video class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;" controls>
                        <source src="${e.target.result}" type="${file.type}">
                    </video>
                    <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0"
                            onclick="this.parentElement.remove()" style="transform: translate(50%, -50%);">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            }

            preview.appendChild(mediaElement);
        };
        reader.readAsDataURL(file);
    });
}

// Enhanced emergency data collection
function collectEmergencyData(type, files, voiceRecording) {
    const description = document.getElementById('emergencyDescription').value;
    const isAnonymous = document.getElementById('anonymousReport').checked;
    const severity = document.querySelector('input[name="severity"]:checked')?.value || 'medium';

    const emergencyData = {
        id: generateEmergencyId(),
        type: type,
        location: {
            lat: mapUserLocation.lat,
            lng: mapUserLocation.lng,
            accuracy: mapUserLocation.accuracy
        },
        description: description,
        severity: severity,
        isAnonymous: isAnonymous,
        timestamp: new Date().toISOString(),
        language: currentLanguage,
        status: 'reported',
        mediaFiles: files || [],
        voiceRecording: voiceRecording,
        reportedBy: isAnonymous ? 'anonymous' : 'user',
        followUpId: generateFollowUpId()
    };

    return emergencyData;
}

function generateEmergencyId() {
    return 'EMG-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

function generateFollowUpId() {
    return 'FU-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6);
}

// Enhanced emergency submission
function submitEnhancedEmergencyReport(emergencyData) {
    // Store in local storage for follow-up tracking
    emergencyReports.push(emergencyData);
    localStorage.setItem('emergencyReports', JSON.stringify(emergencyReports));

    console.log('Enhanced emergency report submitted:', emergencyData);

    // Show upload progress
    showUploadProgress();

    // Simulate upload process
    setTimeout(() => {
        hideUploadProgress();

        // Show success message with follow-up ID
        showSuccessToast(`Emergency reported successfully! Follow-up ID: ${emergencyData.followUpId}`);

        // Add emergency marker to map
        addEmergencyMarkerToMap(emergencyData);

        // Show follow-up options
        showFollowUpOptions(emergencyData);

    }, 2000);
}

function showUploadProgress() {
    const progressBar = document.getElementById('uploadProgress');
    const bar = progressBar.querySelector('.progress-bar');

    progressBar.style.display = 'block';

    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 100) progress = 100;

        bar.style.width = progress + '%';
        bar.textContent = Math.round(progress) + '%';

        if (progress >= 100) {
            clearInterval(interval);
        }
    }, 200);
}

function hideUploadProgress() {
    const progressBar = document.getElementById('uploadProgress');
    progressBar.style.display = 'none';
}

function addEmergencyMarkerToMap(emergencyData) {
    const emergencyIcon = L.divIcon({
        className: 'emergency-marker fire-marker',
        html: '<i class="fas fa-exclamation-triangle" style="color: white; font-size: 14px;"></i>',
        iconSize: [30, 30],
        iconAnchor: [15, 15]
    });

    const emergencyMarker = L.marker([emergencyData.location.lat, emergencyData.location.lng], {
        icon: emergencyIcon
    }).addTo(mapInstance);

    emergencyMarker.bindPopup(`
        <div class="popup-title">Emergency Reported</div>
        <div class="popup-details">
            <strong>ID:</strong> ${emergencyData.id}<br>
            <strong>Type:</strong> ${emergencyData.type}<br>
            <strong>Severity:</strong> <span class="badge bg-${getSeverityColor(emergencyData.severity)}">${emergencyData.severity.toUpperCase()}</span><br>
            <strong>Time:</strong> Just now<br>
            <strong>Status:</strong> ${emergencyData.status}<br>
            <strong>Follow-up ID:</strong> ${emergencyData.followUpId}<br>
            ${emergencyData.description ? `<strong>Description:</strong> ${emergencyData.description}` : ''}
        </div>
        <div class="popup-actions">
            <button class="btn btn-sm btn-info" onclick="trackEmergencyStatus('${emergencyData.followUpId}')">
                <i class="fas fa-search"></i> Track Status
            </button>
        </div>
    `);

    mapEmergencyMarkers.push(emergencyMarker);
}

function callEmergencyServices() {
    const choice = prompt('Which emergency service do you need?\n\n1 - Fire Rescue (118)\n2 - Ambulance (119)\n3 - Police (117)\n\nEnter 1, 2, or 3:');

    switch(choice) {
        case '1':
            window.location.href = 'tel:118';
            break;
        case '2':
            window.location.href = 'tel:119';
            break;
        case '3':
            window.location.href = 'tel:117';
            break;
        default:
            showInfoToast('Emergency Services in Cameroon:\n🔥 Fire: 118 | 🚑 Medical: 119 | 👮 Police: 117');
    }
}

// Dijkstra's Algorithm Implementation for Emergency Facilities
class Graph {
    constructor() {
        this.vertices = new Map();
        this.edges = new Map();
    }

    addVertex(id, lat, lng, data = {}) {
        this.vertices.set(id, { id, lat, lng, ...data });
        this.edges.set(id, new Map());
    }

    addEdge(from, to, weight) {
        if (!this.edges.has(from)) this.edges.set(from, new Map());
        if (!this.edges.has(to)) this.edges.set(to, new Map());

        this.edges.get(from).set(to, weight);
        this.edges.get(to).set(from, weight); // Undirected graph
    }

    dijkstra(startId) {
        const distances = new Map();
        const previous = new Map();
        const unvisited = new Set();

        // Initialize distances
        for (let vertex of this.vertices.keys()) {
            distances.set(vertex, vertex === startId ? 0 : Infinity);
            previous.set(vertex, null);
            unvisited.add(vertex);
        }

        while (unvisited.size > 0) {
            // Find unvisited vertex with minimum distance
            let current = null;
            let minDistance = Infinity;

            for (let vertex of unvisited) {
                if (distances.get(vertex) < minDistance) {
                    minDistance = distances.get(vertex);
                    current = vertex;
                }
            }

            if (current === null || minDistance === Infinity) break;

            unvisited.delete(current);

            // Update distances to neighbors
            const neighbors = this.edges.get(current);
            if (neighbors) {
                for (let [neighbor, weight] of neighbors) {
                    if (unvisited.has(neighbor)) {
                        const newDistance = distances.get(current) + weight;
                        if (newDistance < distances.get(neighbor)) {
                            distances.set(neighbor, newDistance);
                            previous.set(neighbor, current);
                        }
                    }
                }
            }
        }

        return { distances, previous };
    }

    getPath(previous, startId, endId) {
        const path = [];
        let current = endId;

        while (current !== null) {
            path.unshift(current);
            current = previous.get(current);
        }

        return path[0] === startId ? path : [];
    }
}

// Global graph instance
let emergencyGraph = null;

// Initialize emergency network graph
function initializeEmergencyGraph() {
    emergencyGraph = new Graph();

    // Add user location if available
    if (mapUserLocation) {
        emergencyGraph.addVertex('user', mapUserLocation.lat, mapUserLocation.lng, { type: 'user' });
    }

    // Add hospitals
    hospitalData.forEach((hospital, index) => {
        const id = `hospital_${index}`;
        emergencyGraph.addVertex(id, hospital.coordinates[0], hospital.coordinates[1], {
            type: 'hospital',
            name: hospital.name,
            data: hospital
        });
    });

    // Add fire stations
    stationData.forEach((station, index) => {
        const id = `station_${index}`;
        emergencyGraph.addVertex(id, station.coordinates[0], station.coordinates[1], {
            type: 'station',
            name: station.name,
            data: station
        });
    });

    // Add emergency locations
    emergencyData.forEach((emergency, index) => {
        const id = `emergency_${index}`;
        emergencyGraph.addVertex(id, emergency.coordinates[0], emergency.coordinates[1], {
            type: 'emergency',
            title: emergency.title,
            data: emergency
        });
    });

    // Create edges between all vertices (complete graph with distance weights)
    const vertices = Array.from(emergencyGraph.vertices.values());
    for (let i = 0; i < vertices.length; i++) {
        for (let j = i + 1; j < vertices.length; j++) {
            const distance = calculateDistance(
                vertices[i].lat, vertices[i].lng,
                vertices[j].lat, vertices[j].lng
            );
            emergencyGraph.addEdge(vertices[i].id, vertices[j].id, distance);
        }
    }

    console.log('Emergency graph initialized with', emergencyGraph.vertices.size, 'vertices');
}

function findNearestHospital() {
    const targets = findOptimalPath('hospital', 'emergency');
    if (targets && targets.length > 0) {
        showPathfindingResults(targets, 'hospital');
    }
}

function findFireStation() {
    const targets = findOptimalPath('station', 'vehicles');
    if (targets && targets.length > 0) {
        showPathfindingResults(targets, 'station');
    }
}

// Route drawing and visualization
let currentRouteLayer = null;

function drawRoute(startCoords, endCoords, color = '#007bff') {
    // Remove existing route
    if (currentRouteLayer) {
        mapInstance.removeLayer(currentRouteLayer);
    }

    // Create route line
    const routeCoords = [startCoords, endCoords];
    currentRouteLayer = L.polyline(routeCoords, {
        color: color,
        weight: 4,
        opacity: 0.8,
        dashArray: '10, 5'
    }).addTo(mapInstance);

    // Add route markers
    const startIcon = L.divIcon({
        className: 'route-marker start-marker',
        html: '<i class="fas fa-play" style="color: white; font-size: 12px;"></i>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    const endIcon = L.divIcon({
        className: 'route-marker end-marker',
        html: '<i class="fas fa-flag-checkered" style="color: white; font-size: 12px;"></i>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    L.marker(startCoords, { icon: startIcon }).addTo(mapInstance)
        .bindPopup('Start: Your Location');

    L.marker(endCoords, { icon: endIcon }).addTo(mapInstance)
        .bindPopup('Destination');

    // Fit map to show entire route
    const bounds = L.latLngBounds([startCoords, endCoords]);
    mapInstance.fitBounds(bounds, { padding: [20, 20] });
}

function clearRoute() {
    if (currentRouteLayer) {
        mapInstance.removeLayer(currentRouteLayer);
        currentRouteLayer = null;
    }
}

// Advanced pathfinding with multiple criteria
function findOptimalPath(targetType, criteria = 'distance') {
    if (!mapUserLocation) {
        showErrorToast('Please get your location first by clicking "My Location" button.');
        return;
    }

    if (!emergencyGraph) {
        initializeEmergencyGraph();
    }

    // Update user location in graph
    updateUserLocationInGraph();

    // Run Dijkstra's algorithm
    const { distances, previous } = emergencyGraph.dijkstra('user');

    // Find all targets of specified type
    const targets = [];
    for (let [vertexId, distance] of distances) {
        const vertex = emergencyGraph.vertices.get(vertexId);
        if (vertex.type === targetType) {
            targets.push({
                vertex: vertex,
                distance: distance,
                path: emergencyGraph.getPath(previous, 'user', vertexId)
            });
        }
    }

    if (targets.length === 0) {
        showErrorToast(`No ${targetType}s found in the network.`);
        return;
    }

    // Sort by criteria
    targets.sort((a, b) => {
        switch (criteria) {
            case 'distance':
                return a.distance - b.distance;
            case 'vehicles': // For fire stations
                if (targetType === 'station') {
                    return (b.vertex.data?.vehicles || 0) - (a.vertex.data?.vehicles || 0);
                }
                return a.distance - b.distance;
            case 'emergency': // For hospitals with emergency services
                if (targetType === 'hospital') {
                    const aEmergency = a.vertex.data?.emergency ? 0 : 1;
                    const bEmergency = b.vertex.data?.emergency ? 0 : 1;
                    return aEmergency - bEmergency || a.distance - b.distance;
                }
                return a.distance - b.distance;
            default:
                return a.distance - b.distance;
        }
    });

    return targets;
}

function showPathfindingResults(targets, targetType) {
    if (targets.length === 0) return;

    const nearest = targets[0];

    // Center map and highlight
    mapInstance.setView([nearest.vertex.lat, nearest.vertex.lng], 14);

    // Draw route
    const color = targetType === 'hospital' ? '#17a2b8' : '#ffc107';
    drawRoute([mapUserLocation.lat, mapUserLocation.lng], [nearest.vertex.lat, nearest.vertex.lng], color);

    // Create results modal
    const modalHtml = `
        <div class="modal fade" id="pathfindingModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-route me-2"></i>Pathfinding Results - ${targetType === 'hospital' ? 'Hospitals' : 'Fire Stations'}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Showing ${targets.length} ${targetType}(s) sorted by distance using Dijkstra's algorithm
                        </div>
                        <div class="list-group">
                            ${targets.slice(0, 5).map((target, index) => `
                                <div class="list-group-item ${index === 0 ? 'list-group-item-primary' : ''}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                ${index === 0 ? '<i class="fas fa-star text-warning me-1"></i>' : ''}
                                                ${target.vertex.name || target.vertex.title}
                                                ${index === 0 ? ' (Nearest)' : ''}
                                            </h6>
                                            <p class="mb-1">
                                                <strong>Distance:</strong> ${(target.distance/1000).toFixed(2)} km<br>
                                                ${targetType === 'hospital' ?
                                                    `<strong>Emergency Services:</strong> ${target.vertex.data?.emergency ? 'Available' : 'Not Available'}<br>` :
                                                    `<strong>Vehicles:</strong> ${target.vertex.data?.vehicles || 'N/A'}<br>`
                                                }
                                                <strong>Phone:</strong> ${target.vertex.data?.phone || 'N/A'}
                                            </p>
                                        </div>
                                        <div class="text-end">
                                            <button class="btn btn-sm btn-outline-primary" onclick="showPathTo('${target.vertex.id}')">
                                                <i class="fas fa-route"></i> Show Route
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        ${targets.length > 5 ? `<p class="mt-2 text-muted">Showing top 5 of ${targets.length} results</p>` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="clearRoute()">Clear Route</button>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('pathfindingModal');
    if (existingModal) existingModal.remove();

    // Add and show modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('pathfindingModal'));
    modal.show();

    // Show success message
    showSuccessToast(`Found ${targets.length} ${targetType}(s). Nearest: ${nearest.vertex.name} (${(nearest.distance/1000).toFixed(2)} km)`);
}

function showPathTo(vertexId) {
    const vertex = emergencyGraph.vertices.get(vertexId);
    if (vertex && mapUserLocation) {
        const color = vertex.type === 'hospital' ? '#17a2b8' : '#ffc107';
        drawRoute([mapUserLocation.lat, mapUserLocation.lng], [vertex.lat, vertex.lng], color);
        mapInstance.setView([vertex.lat, vertex.lng], 14);

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('pathfindingModal'));
        if (modal) modal.hide();
    }
}

function updateUserLocationInGraph() {
    if (!emergencyGraph || !mapUserLocation) return;

    if (emergencyGraph.vertices.has('user')) {
        emergencyGraph.vertices.set('user', {
            id: 'user',
            lat: mapUserLocation.lat,
            lng: mapUserLocation.lng,
            type: 'user'
        });
    } else {
        emergencyGraph.addVertex('user', mapUserLocation.lat, mapUserLocation.lng, { type: 'user' });
        // Add edges to all existing vertices
        const vertices = Array.from(emergencyGraph.vertices.values());
        for (let vertex of vertices) {
            if (vertex.id !== 'user') {
                const distance = calculateDistance(
                    mapUserLocation.lat, mapUserLocation.lng,
                    vertex.lat, vertex.lng
                );
                emergencyGraph.addEdge('user', vertex.id, distance);
            }
        }
    }
}

// Follow-up tracking and status monitoring
function showFollowUpOptions(emergencyData) {
    const followUpHtml = `
        <div class="modal fade" id="followUpModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-check-circle me-2"></i>Emergency Reported Successfully
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-info-circle me-2"></i>Your Report Details</h6>
                            <p><strong>Emergency ID:</strong> ${emergencyData.id}</p>
                            <p><strong>Follow-up ID:</strong> ${emergencyData.followUpId}</p>
                            <p><strong>Type:</strong> ${emergencyData.type}</p>
                            <p><strong>Severity:</strong> ${emergencyData.severity}</p>
                            <p><strong>Status:</strong> Reported and being processed</p>
                        </div>

                        <h6>What happens next?</h6>
                        <ol class="small">
                            <li>Emergency services have been automatically notified</li>
                            <li>Your report is being reviewed and prioritized</li>
                            <li>Response teams will be dispatched based on severity</li>
                            <li>You can track the status using your Follow-up ID</li>
                        </ol>

                        <div class="mt-3">
                            <h6>Immediate Actions:</h6>
                            <div class="d-grid gap-2">
                                <button class="btn btn-danger" onclick="callEmergencyServices()">
                                    <i class="fas fa-phone me-2"></i>Call Emergency Services Now
                                </button>
                                <button class="btn btn-info" onclick="trackEmergencyStatus('${emergencyData.followUpId}')">
                                    <i class="fas fa-search me-2"></i>Track Status
                                </button>
                                <button class="btn btn-warning" onclick="shareEmergencyReport('${emergencyData.followUpId}')">
                                    <i class="fas fa-share-alt me-2"></i>Share with Family/Friends
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="viewMyReports()">
                            <i class="fas fa-list me-1"></i>View My Reports
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('followUpModal');
    if (existingModal) existingModal.remove();

    // Add and show modal
    document.body.insertAdjacentHTML('beforeend', followUpHtml);
    const modal = new bootstrap.Modal(document.getElementById('followUpModal'));
    modal.show();
}

function trackEmergencyStatus(followUpId) {
    // Find the emergency report
    const report = emergencyReports.find(r => r.followUpId === followUpId);

    if (!report) {
        showErrorToast('Emergency report not found. Please check your Follow-up ID.');
        return;
    }

    // Simulate status updates
    const possibleStatuses = [
        { status: 'reported', description: 'Report received and logged', time: '0 min ago' },
        { status: 'reviewing', description: 'Report under review by dispatch', time: '2 min ago' },
        { status: 'dispatched', description: 'Emergency units dispatched', time: '5 min ago' },
        { status: 'en_route', description: 'Emergency units en route', time: '8 min ago' },
        { status: 'on_scene', description: 'Emergency units on scene', time: '15 min ago' },
        { status: 'resolved', description: 'Emergency resolved', time: '45 min ago' }
    ];

    // Simulate progression based on time since report
    const timeSinceReport = Date.now() - new Date(report.timestamp).getTime();
    const minutesSince = Math.floor(timeSinceReport / (1000 * 60));

    let currentStatusIndex = 0;
    if (minutesSince > 45) currentStatusIndex = 5;
    else if (minutesSince > 15) currentStatusIndex = 4;
    else if (minutesSince > 8) currentStatusIndex = 3;
    else if (minutesSince > 5) currentStatusIndex = 2;
    else if (minutesSince > 2) currentStatusIndex = 1;

    const currentStatus = possibleStatuses[currentStatusIndex];

    showStatusTrackingModal(report, possibleStatuses, currentStatusIndex);
}

function showStatusTrackingModal(report, statuses, currentIndex) {
    const statusHtml = `
        <div class="modal fade" id="statusModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-search me-2"></i>Emergency Status Tracking
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Report Information</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Follow-up ID:</strong></td><td>${report.followUpId}</td></tr>
                                    <tr><td><strong>Type:</strong></td><td>${report.type}</td></tr>
                                    <tr><td><strong>Severity:</strong></td><td><span class="badge bg-${getSeverityColor(report.severity)}">${report.severity}</span></td></tr>
                                    <tr><td><strong>Reported:</strong></td><td>${new Date(report.timestamp).toLocaleString()}</td></tr>
                                    <tr><td><strong>Status:</strong></td><td><span class="badge bg-primary">${statuses[currentIndex].status.replace('_', ' ').toUpperCase()}</span></td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Status Timeline</h6>
                                <div class="timeline">
                                    ${statuses.map((status, index) => `
                                        <div class="timeline-item ${index <= currentIndex ? 'completed' : 'pending'}">
                                            <div class="timeline-marker ${index <= currentIndex ? 'bg-success' : 'bg-secondary'}">
                                                <i class="fas ${index <= currentIndex ? 'fa-check' : 'fa-clock'}"></i>
                                            </div>
                                            <div class="timeline-content">
                                                <h6 class="mb-1">${status.status.replace('_', ' ').toUpperCase()}</h6>
                                                <p class="mb-0 small">${status.description}</p>
                                                ${index <= currentIndex ? `<small class="text-muted">${status.time}</small>` : ''}
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Current Status</h6>
                                <p class="mb-0">${statuses[currentIndex].description}</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="refreshStatus('${report.followUpId}')">
                            <i class="fas fa-refresh me-1"></i>Refresh Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('statusModal');
    if (existingModal) existingModal.remove();

    // Add and show modal
    document.body.insertAdjacentHTML('beforeend', statusHtml);
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function refreshStatus(followUpId) {
    showInfoToast('Refreshing status...');
    setTimeout(() => {
        trackEmergencyStatus(followUpId);
    }, 1000);
}

function shareEmergencyReport(followUpId) {
    const report = emergencyReports.find(r => r.followUpId === followUpId);
    if (!report) return;

    const shareText = `Emergency Report Update\n\nFollow-up ID: ${followUpId}\nType: ${report.type}\nSeverity: ${report.severity}\nLocation: ${report.location.lat.toFixed(4)}, ${report.location.lng.toFixed(4)}\nTime: ${new Date(report.timestamp).toLocaleString()}\n\nTrack status at: [Emergency App]`;

    if (navigator.share) {
        navigator.share({
            title: 'Emergency Report',
            text: shareText
        });
    } else {
        navigator.clipboard.writeText(shareText);
        showSuccessToast('Emergency report details copied to clipboard!');
    }
}

function viewMyReports() {
    if (emergencyReports.length === 0) {
        showInfoToast('No emergency reports found.');
        return;
    }

    const reportsHtml = `
        <div class="modal fade" id="myReportsModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-list me-2"></i>My Emergency Reports
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Follow-up ID</th>
                                        <th>Type</th>
                                        <th>Severity</th>
                                        <th>Date/Time</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${emergencyReports.slice(-10).reverse().map(report => `
                                        <tr>
                                            <td><code>${report.followUpId}</code></td>
                                            <td><i class="fas fa-${getTypeIcon(report.type)} me-1"></i>${report.type}</td>
                                            <td><span class="badge bg-${getSeverityColor(report.severity)}">${report.severity}</span></td>
                                            <td>${new Date(report.timestamp).toLocaleString()}</td>
                                            <td><span class="badge bg-info">${report.status}</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" onclick="trackEmergencyStatus('${report.followUpId}')">
                                                    <i class="fas fa-search"></i> Track
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        ${emergencyReports.length > 10 ? `<p class="text-muted">Showing last 10 reports of ${emergencyReports.length} total.</p>` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-danger" onclick="clearAllReports()">
                            <i class="fas fa-trash me-1"></i>Clear All Reports
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('myReportsModal');
    if (existingModal) existingModal.remove();

    // Add and show modal
    document.body.insertAdjacentHTML('beforeend', reportsHtml);
    const modal = new bootstrap.Modal(document.getElementById('myReportsModal'));
    modal.show();
}

function getTypeIcon(type) {
    const icons = {
        fire: 'fire',
        medical: 'ambulance',
        accident: 'car-crash',
        crime: 'shield-alt',
        other: 'exclamation-triangle'
    };
    return icons[type] || 'exclamation-triangle';
}

function clearAllReports() {
    if (confirm('Are you sure you want to clear all emergency reports? This action cannot be undone.')) {
        emergencyReports = [];
        localStorage.removeItem('emergencyReports');
        showSuccessToast('All emergency reports cleared.');

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('myReportsModal'));
        if (modal) modal.hide();
    }
}

// Advanced pathfinding control functions
function findOptimalHospitals(criteria) {
    const targets = findOptimalPath('hospital', criteria);
    if (targets && targets.length > 0) {
        showPathfindingResults(targets, 'hospital');
    }
}

function findOptimalStations(criteria) {
    const targets = findOptimalPath('station', criteria);
    if (targets && targets.length > 0) {
        showPathfindingResults(targets, 'station');
    }
}

function showGraphInfo() {
    if (!emergencyGraph) {
        initializeEmergencyGraph();
    }

    const vertices = emergencyGraph.vertices.size;
    const hospitals = Array.from(emergencyGraph.vertices.values()).filter(v => v.type === 'hospital').length;
    const stations = Array.from(emergencyGraph.vertices.values()).filter(v => v.type === 'station').length;
    const emergencies = Array.from(emergencyGraph.vertices.values()).filter(v => v.type === 'emergency').length;

    const infoHtml = `
        <div class="modal fade" id="graphInfoModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-project-diagram me-2"></i>Emergency Network Graph
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Dijkstra's Algorithm</h6>
                            <p class="mb-0">This system uses Dijkstra's algorithm to find the shortest paths between your location and emergency facilities. The algorithm guarantees optimal routes based on real distances.</p>
                        </div>

                        <h6>Network Statistics:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-circle text-primary me-2"></i>Total Vertices</span>
                                <strong>${vertices}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-hospital text-info me-2"></i>Hospitals</span>
                                <strong>${hospitals}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-truck text-warning me-2"></i>Fire Stations</span>
                                <strong>${stations}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-fire text-danger me-2"></i>Active Emergencies</span>
                                <strong>${emergencies}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-route text-success me-2"></i>Total Edges</span>
                                <strong>${vertices * (vertices - 1) / 2}</strong>
                            </li>
                        </ul>

                        <div class="mt-3">
                            <h6>Algorithm Features:</h6>
                            <ul class="small">
                                <li>✅ Optimal shortest path calculation</li>
                                <li>✅ Real-time distance computation</li>
                                <li>✅ Multiple sorting criteria</li>
                                <li>✅ Visual route display</li>
                                <li>✅ Complete graph connectivity</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('graphInfoModal');
    if (existingModal) existingModal.remove();

    // Add and show modal
    document.body.insertAdjacentHTML('beforeend', infoHtml);
    const modal = new bootstrap.Modal(document.getElementById('graphInfoModal'));
    modal.show();
}

// Supporting functions for enhanced map functionality

function showAllHospitals() {
    clearHospitalMarkers();
    hospitalData.forEach(hospital => {
        addHospitalMarker(hospital);
    });
    showSuccessToast(`Showing ${hospitalData.length} hospitals across Cameroon`);
}

function showAllFireStations() {
    clearStationMarkers();
    stationData.forEach(station => {
        addFireStationMarker(station);
    });
    showSuccessToast(`Showing ${stationData.length} fire stations across Cameroon`);
}

function clearHospitalMarkers() {
    mapHospitalMarkers.forEach(marker => {
        mapInstance.removeLayer(marker);
    });
    mapHospitalMarkers = [];
    clearAllRoutes(); // Also clear arrows when clearing markers
}

function clearStationMarkers() {
    mapStationMarkers.forEach(marker => {
        mapInstance.removeLayer(marker);
    });
    mapStationMarkers = [];
    clearAllRoutes(); // Also clear arrows when clearing markers
}

function clearRoute() {
    // Remove route polylines if they exist
    if (window.currentRoute) {
        mapInstance.removeLayer(window.currentRoute);
        window.currentRoute = null;
    }
    showInfoToast('Route cleared');
}

function clearAllRoutes() {
    // Clear all route arrows
    mapRouteArrows.forEach(arrow => {
        mapInstance.removeLayer(arrow);
    });
    mapRouteArrows = [];

    // Clear all distance labels
    mapDistanceLabels.forEach(label => {
        mapInstance.removeLayer(label);
    });
    mapDistanceLabels = [];

    // Clear single route if exists
    clearRoute();
}

function drawArrowWithDistance(start, destination, type, isNearest = false) {
    const startCoords = [start.lat, start.lng];
    const endCoords = [destination.coordinates[0], destination.coordinates[1]];

    // Choose colors based on type and if it's nearest
    let arrowColor, arrowWeight, arrowOpacity;
    if (isNearest) {
        arrowColor = type === 'hospital' ? '#dc3545' : '#ff6b35'; // Red/Orange for nearest
        arrowWeight = 5;
        arrowOpacity = 1.0;
    } else {
        arrowColor = type === 'hospital' ? '#17a2b8' : '#ffc107'; // Blue/Yellow for others
        arrowWeight = 3;
        arrowOpacity = 0.7;
    }

    // Create arrow polyline
    const arrow = L.polyline([startCoords, endCoords], {
        color: arrowColor,
        weight: arrowWeight,
        opacity: arrowOpacity,
        dashArray: isNearest ? null : '10, 5' // Solid line for nearest, dashed for others
    }).addTo(mapInstance);

    // Add arrowhead at the end
    const arrowHead = L.polylineDecorator(arrow, {
        patterns: [{
            offset: '100%',
            repeat: 0,
            symbol: L.Symbol.arrowHead({
                pixelSize: isNearest ? 15 : 12,
                polygon: false,
                pathOptions: {
                    stroke: true,
                    weight: 2,
                    color: arrowColor,
                    opacity: arrowOpacity
                }
            })
        }]
    });

    // Add to map if decorator is available, otherwise just use the line
    if (typeof L.polylineDecorator !== 'undefined') {
        arrowHead.addTo(mapInstance);
        mapRouteArrows.push(arrowHead);
    }
    mapRouteArrows.push(arrow);

    // Calculate midpoint for distance label
    const midLat = (startCoords[0] + endCoords[0]) / 2;
    const midLng = (startCoords[1] + endCoords[1]) / 2;

    // Create distance label
    const distance = destination.distance.toFixed(2);
    const labelText = isNearest ? `${distance}km\nNEAREST` : `${distance}km`;

    const distanceLabel = L.marker([midLat, midLng], {
        icon: L.divIcon({
            className: 'distance-label',
            html: `
                <div style="
                    background: ${isNearest ? '#dc3545' : '#ffffff'};
                    color: ${isNearest ? '#ffffff' : '#333333'};
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: ${isNearest ? 'bold' : 'normal'};
                    text-align: center;
                    border: 2px solid ${arrowColor};
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    white-space: pre-line;
                    min-width: 50px;
                ">
                    ${labelText}
                </div>
            `,
            iconSize: [60, isNearest ? 35 : 25],
            iconAnchor: [30, isNearest ? 17 : 12]
        })
    }).addTo(mapInstance);

    mapDistanceLabels.push(distanceLabel);

    console.log(`🏹 Drew arrow to ${destination.name}: ${distance}km ${isNearest ? '(NEAREST)' : ''}`);
}

function drawRoute(start, destination, type) {
    // Clear existing route
    clearRoute();

    // Create route polyline
    const routeCoords = [
        [start.lat, start.lng],
        [destination.coordinates[0], destination.coordinates[1]]
    ];

    const routeColor = type === 'hospital' ? '#17a2b8' : '#ffc107';

    window.currentRoute = L.polyline(routeCoords, {
        color: routeColor,
        weight: 4,
        opacity: 0.8,
        dashArray: '10, 10'
    }).addTo(mapInstance);

    // Fit map to show entire route
    const group = new L.featureGroup([window.currentRoute]);
    mapInstance.fitBounds(group.getBounds().pad(0.1));
}

function focusOnLocation(lat, lng, name) {
    mapInstance.setView([lat, lng], 16);
    showInfoToast(`Focused on ${name}`);

    // Close modal if open
    const modal = bootstrap.Modal.getInstance(document.getElementById('resultsModal'));
    if (modal) modal.hide();
}

function callHospital(phone) {
    if (confirm(`Call ${phone}?`)) {
        window.location.href = `tel:${phone}`;
    }
}

function callFireStation(phone) {
    if (confirm(`Call ${phone}?`)) {
        window.location.href = `tel:${phone}`;
    }
}

function shareLocation() {
    if (mapUserLocation) {
        const shareData = {
            title: 'My Emergency Location',
            text: `I'm at coordinates: ${mapUserLocation.lat.toFixed(6)}, ${mapUserLocation.lng.toFixed(6)}`,
            url: `https://www.google.com/maps?q=${mapUserLocation.lat},${mapUserLocation.lng}`
        };

        if (navigator.share) {
            navigator.share(shareData);
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareData.url).then(() => {
                showSuccessToast('Location link copied to clipboard!');
            }).catch(() => {
                alert(shareData.text);
            });
        }
    } else {
        showErrorToast('Please get your location first');
    }
}

function callFireRescue() {
    if (confirm('Call Fire Rescue (118)?')) {
        window.location.href = 'tel:118';
    }
}

function callAmbulance() {
    if (confirm('Call Ambulance (119)?')) {
        window.location.href = 'tel:119';
    }
}

function getDirections(lat, lng, name) {
    if (!mapUserLocation) {
        showErrorToast('Please get your location first');
        return;
    }

    const googleMapsUrl = `https://www.google.com/maps/dir/${mapUserLocation.lat},${mapUserLocation.lng}/${lat},${lng}`;
    window.open(googleMapsUrl, '_blank');
    showInfoToast(`Opening directions to ${name || 'destination'} in Google Maps`);
}

// Toast notification functions
function showSuccessToast(message) {
    showToast(message, 'success', 'fas fa-check-circle');
}

function showErrorToast(message) {
    showToast(message, 'danger', 'fas fa-exclamation-triangle');
}

function showInfoToast(message) {
    showToast(message, 'info', 'fas fa-info-circle');
}

function showToast(message, type, icon) {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.custom-toast');
    existingToasts.forEach(toast => toast.remove());

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `custom-toast alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
        border-radius: 8px;
    `;

    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="${icon} me-2"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 150);
        }
    }, 5000);
}

// Initialize map function
function initializeMap() {
    console.log('Initializing map...');
    const mapElement = document.getElementById('map');
    const loadingElement = document.getElementById('map-loading');
    console.log('Map element found:', mapElement);
    console.log('Loading element found:', loadingElement);

    if (!mapElement) {
        console.error('Map element not found!');
        return;
    }

    // Test if Leaflet is loaded
    if (typeof L === 'undefined') {
        console.error('Leaflet not loaded!');
        mapElement.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Error: Leaflet library not loaded</div>';
        return;
    }

    console.log('Leaflet loaded successfully');

    try {
        // Clear loading message
        if (loadingElement) {
            loadingElement.remove();
        }

        // Create map
        console.log('Creating map with center:', CAMEROON_CENTER);
        mapInstance = L.map('map').setView(CAMEROON_CENTER, 6);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(mapInstance);

        console.log('Map tiles added, now adding markers...');

        // Add markers
        addMarkers();

        console.log('Map created successfully!');

    } catch (error) {
        console.error('Error creating map:', error);
        mapElement.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Error creating map: ' + error.message + '</div>';
    }
}

// Add markers function
function addMarkers() {
    try {
        console.log('Adding markers...');
        console.log('Emergency data:', emergencyData);
        console.log('Hospital data:', hospitalData);
        console.log('Station data:', stationData);

        // Add emergency markers
        emergencyData.forEach((emergency, index) => {
            console.log(`Adding emergency marker ${index}:`, emergency.coordinates);
            const marker = L.marker(emergency.coordinates)
                .addTo(mapInstance)
                .bindPopup(`
                    <div><strong>${emergency.title}</strong></div>
                    <div>Location: ${emergency.location}</div>
                    <div>Severity: ${emergency.severity}</div>
                    <div>Time: ${emergency.time}</div>
                `);
            mapEmergencyMarkers.push(marker);
        });

        // Add hospital markers with enhanced functionality
        hospitalData.forEach((hospital, index) => {
            console.log(`Adding hospital marker ${index}:`, hospital.coordinates);
            addHospitalMarker(hospital);
        });

        // Add fire station markers with enhanced functionality
        stationData.forEach((station, index) => {
            console.log(`Adding station marker ${index}:`, station.coordinates);
            addFireStationMarker(station);
        });

        console.log('All markers added successfully');
        console.log('Emergency markers:', mapEmergencyMarkers.length);
        console.log('Hospital markers:', mapHospitalMarkers.length);
        console.log('Station markers:', mapStationMarkers.length);

    } catch (error) {
        console.error('Error adding markers:', error);
    }
}

// Test map function for button
function testMap() {
    initializeMap();
}

// Initialize map when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing map...');

    // Initialize language preference
    const savedLanguage = localStorage.getItem('preferredLanguage');
    if (savedLanguage) {
        currentLanguage = savedLanguage;
    }

    // Load emergency reports from localStorage
    const savedReports = localStorage.getItem('emergencyReports');
    if (savedReports) {
        emergencyReports = JSON.parse(savedReports);
    }

    // Wait a bit for everything to load
    setTimeout(function() {
        initializeMap();
    }, 1000);
});
</script>
{% endblock %}
