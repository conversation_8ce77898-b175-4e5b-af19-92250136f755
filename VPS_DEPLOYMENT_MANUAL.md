# 🚀 Emergency Response App - Manual VPS Deployment Guide

## 📋 VPS Information
- **IP Address**: *************
- **Hostname**: srv838312.hstgr.cloud
- **User**: root
- **SSH Command**: `ssh root@*************`

## 🎯 Deployment Steps

### Step 1: Test SSH Connection
```bash
ssh root@*************
```
**Expected**: You should be able to connect to your VPS without issues.

### Step 2: Update System and Install Dependencies
```bash
# Update package cache
apt update

# Install required packages
apt install -y python3 python3-pip python3-venv git nginx sqlite3 curl wget unzip htop vim ufw

# Enable and start nginx
systemctl enable nginx
systemctl start nginx
```

### Step 3: Create Application User and Directories
```bash
# Create emergency user
useradd -r -s /bin/bash -d /opt/emergency-app emergency

# Create directories
mkdir -p /opt/emergency-app
mkdir -p /var/log/emergency-app

# Set ownership
chown emergency:emergency /opt/emergency-app
chown emergency:emergency /var/log/emergency-app
```

### Step 4: Upload Application Files
From your local machine, upload the files:

```bash
# Upload main Python files
scp app.py root@*************:/opt/emergency-app/
scp database.py root@*************:/opt/emergency-app/
scp auth.py root@*************:/opt/emergency-app/
scp api_endpoints.py root@*************:/opt/emergency-app/

# Upload templates and static directories
scp -r templates root@*************:/opt/emergency-app/
scp -r static root@*************:/opt/emergency-app/

# Upload monitoring configuration
scp docker-compose.monitoring.yml root@*************:/opt/emergency-app/
scp -r monitoring root@*************:/opt/emergency-app/
```

### Step 5: Setup Python Environment (on VPS)
```bash
# Change to app directory
cd /opt/emergency-app

# Create virtual environment
python3 -m venv venv

# Activate virtual environment and install packages
source venv/bin/activate
pip install flask flask-login prometheus-client prometheus-flask-exporter bcrypt email-validator

# Set ownership
chown -R emergency:emergency /opt/emergency-app
```

### Step 6: Create Systemd Service (on VPS)
```bash
# Create systemd service file
cat > /etc/systemd/system/emergency-app.service << 'EOF'
[Unit]
Description=Emergency Response App
After=network.target

[Service]
Type=simple
User=emergency
Group=emergency
WorkingDirectory=/opt/emergency-app
Environment=PATH=/opt/emergency-app/venv/bin
ExecStart=/opt/emergency-app/venv/bin/python app.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=emergency-app

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
systemctl daemon-reload
systemctl enable emergency-app
```

### Step 7: Configure Nginx Reverse Proxy (on VPS)
```bash
# Create nginx configuration
cat > /etc/nginx/sites-available/emergency-app << 'EOF'
server {
    listen 80;
    server_name srv838312.hstgr.cloud *************;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /metrics {
        proxy_pass http://127.0.0.1:3000/metrics;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    # Logging
    access_log /var/log/nginx/emergency-app-access.log;
    error_log /var/log/nginx/emergency-app-error.log;
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/emergency-app /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
nginx -t

# Reload nginx
systemctl reload nginx
```

### Step 8: Configure Firewall (on VPS)
```bash
# Reset firewall
ufw --force reset

# Set default policies
ufw default deny incoming
ufw default allow outgoing

# Allow necessary ports
ufw allow 22      # SSH
ufw allow 80      # HTTP
ufw allow 443     # HTTPS
ufw allow 3000    # Emergency App
ufw allow 9090    # Prometheus
ufw allow 3001    # Grafana
ufw allow 9093    # Alertmanager
ufw allow 9100    # Node Exporter

# Enable firewall
ufw --force enable
```

### Step 9: Start the Application (on VPS)
```bash
# Start the emergency app service
systemctl start emergency-app

# Check service status
systemctl status emergency-app

# Check if app is responding
curl http://localhost:3000/api/v1/health
```

### Step 10: Install Docker for Monitoring (on VPS)
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-linux-x86_64" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Start Docker service
systemctl enable docker
systemctl start docker
```

### Step 11: Start Monitoring Stack (on VPS)
```bash
# Change to app directory
cd /opt/emergency-app

# Start monitoring services
docker-compose -f docker-compose.monitoring.yml up -d

# Check container status
docker ps
```

## ✅ Verification Steps

### 1. Check Service Status
```bash
# Check all services
systemctl status emergency-app nginx docker

# Check application logs
journalctl -u emergency-app -f
```

### 2. Test Application Endpoints
```bash
# Test health endpoint
curl http://localhost:3000/api/v1/health

# Test metrics endpoint
curl http://localhost:3000/metrics

# Test API with external access
curl http://*************/api/v1/health
```

### 3. Test Monitoring Services
```bash
# Check Prometheus
curl http://localhost:9090/-/healthy

# Check Grafana
curl http://localhost:3001/api/health

# List running containers
docker ps
```

## 🌐 Access URLs

### Application URLs
- **Main App**: http://*************
- **Main App**: http://srv838312.hstgr.cloud
- **API Health**: http://*************/api/v1/health
- **Metrics**: http://*************/metrics

### Monitoring URLs (after starting monitoring stack)
- **Prometheus**: http://*************:9090
- **Grafana**: http://*************:3001 (admin/emergency123)
- **Alertmanager**: http://*************:9093
- **Node Exporter**: http://*************:9100

## 🔧 Troubleshooting

### Application Won't Start
```bash
# Check logs
journalctl -u emergency-app --no-pager

# Check file permissions
ls -la /opt/emergency-app/

# Test Python environment
cd /opt/emergency-app
source venv/bin/activate
python app.py
```

### Nginx Issues
```bash
# Test nginx configuration
nginx -t

# Check nginx logs
tail -f /var/log/nginx/error.log

# Restart nginx
systemctl restart nginx
```

### Docker Issues
```bash
# Check Docker service
systemctl status docker

# Check container logs
docker logs emergency-prometheus
docker logs emergency-grafana

# Restart monitoring stack
cd /opt/emergency-app
docker-compose -f docker-compose.monitoring.yml down
docker-compose -f docker-compose.monitoring.yml up -d
```

### Firewall Issues
```bash
# Check firewall status
ufw status

# Check if ports are open
netstat -tulpn | grep -E ':(3000|9090|3001|9093)'
```

## 📊 Expected Results

### Successful Deployment Indicators
- ✅ Emergency app service: `active (running)`
- ✅ Nginx service: `active (running)`
- ✅ Docker service: `active (running)`
- ✅ Health endpoint returns: `{"status":"success","data":{"status":"healthy"}}`
- ✅ Monitoring containers: 4 containers running
- ✅ External access: App accessible via browser

### Performance Benchmarks
- **Application startup**: <30 seconds
- **Health check response**: <2 seconds
- **Memory usage**: <512MB
- **Disk usage**: <2GB total

## 🎉 Success!

Once all steps are completed successfully, your Emergency Response App will be:

1. **Running on your VPS** with proper service management
2. **Accessible via web browser** at your domain and IP
3. **API endpoints available** for integration
4. **Monitoring stack operational** with Prometheus and Grafana
5. **Secured with firewall** and proper permissions
6. **Auto-starting on boot** via systemd

Your app is now production-ready and accessible to users worldwide! 🌍
