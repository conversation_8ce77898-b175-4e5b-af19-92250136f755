#!/bin/bash
# Emergency Response App Backup Script
# Generated by Ansible on {{ ansible_date_time.iso8601 }}

set -e

# Configuration
APP_DIR="{{ app_directory }}"
BACKUP_DIR="{{ app_directory }}/backups"
DB_FILE="{{ app_directory }}/emergency_app.db"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="emergency_app_backup_${DATE}"
RETENTION_DAYS=30

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

# Create backup
echo "Starting backup at $(date)"

# Backup database
if [ -f "${DB_FILE}" ]; then
    echo "Backing up database..."
    cp "${DB_FILE}" "${BACKUP_DIR}/${BACKUP_NAME}.db"
else
    echo "Warning: Database file not found at ${DB_FILE}"
fi

# Backup application files
echo "Backing up application files..."
tar -czf "${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz" \
    -C "${APP_DIR}" \
    --exclude="backups" \
    --exclude="venv" \
    --exclude="__pycache__" \
    --exclude="*.pyc" \
    --exclude="*.log" \
    .

# Backup monitoring configuration
echo "Backing up monitoring configuration..."
if [ -d "${APP_DIR}/monitoring" ]; then
    tar -czf "${BACKUP_DIR}/${BACKUP_NAME}_monitoring.tar.gz" \
        -C "${APP_DIR}" \
        monitoring/
fi

# Create backup manifest
echo "Creating backup manifest..."
cat > "${BACKUP_DIR}/${BACKUP_NAME}_manifest.txt" << EOF
Backup created: $(date)
Hostname: {{ ansible_hostname }}
App directory: ${APP_DIR}
Database file: ${DB_FILE}
Backup files:
- ${BACKUP_NAME}.db
- ${BACKUP_NAME}_files.tar.gz
- ${BACKUP_NAME}_monitoring.tar.gz
EOF

# Cleanup old backups
echo "Cleaning up old backups (older than ${RETENTION_DAYS} days)..."
find "${BACKUP_DIR}" -name "emergency_app_backup_*" -mtime +${RETENTION_DAYS} -delete

# Verify backup
echo "Verifying backup..."
if [ -f "${BACKUP_DIR}/${BACKUP_NAME}.db" ] && [ -f "${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz" ]; then
    echo "Backup completed successfully at $(date)"
    echo "Backup location: ${BACKUP_DIR}/${BACKUP_NAME}*"
    
    # Log backup size
    BACKUP_SIZE=$(du -sh "${BACKUP_DIR}/${BACKUP_NAME}"* | awk '{print $1}' | paste -sd+ | bc 2>/dev/null || echo "Unknown")
    echo "Total backup size: ${BACKUP_SIZE}"
else
    echo "Error: Backup verification failed!"
    exit 1
fi
